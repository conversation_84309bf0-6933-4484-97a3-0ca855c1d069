@extends('layouts.backend')

@section('title', __('Whatsapp Webhook Settings'))

@section('content')
    <!-- main Section -->
    <div class="main-body">
        <div class="container-fluid">

            <div class="row mt-25">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header">{{ __('Settings') }}</div>
                        <div class="card-body tabs-area p-0">
                            @include('backend.partials.settings_tabs_nav')
                            <div class="tabs-body">
                                <!--General Setting-->
                                <div id="GlobalSetting">
                                    <form novalidate="" data-validate="parsley" id="DataEntry_formId">
                                        <div class="row">
                                            <div class="col-lg-8">
                                                <div class="divider_heading">{{ __('Whatsapp Webhook Settings') }}</div>
                                                <div class="form-group">
                                                    <label for="whatsapp_webhook_api_version">{{ __('Whatsapp Webhook API Version') }}<span
                                                            class="red">*</span></label>
                                                    <input type="text" name="whatsapp_webhook_api_version" id="whatsapp_webhook_api_version"
                                                        class="form-control parsley-validated" data-required="true"
                                                        value="{{ $dataList['whatsapp_webhook_api_version'] }}">
                                                </div>
                                                <div class="form-group">
                                                    <label for="whatsapp_phone_number_id">{{ __('Whatsapp Phone Number ID') }}<span
                                                            class="red">*</span></label>
                                                    <input type="text" name="whatsapp_phone_number_id" id="whatsapp_phone_number_id"
                                                        class="form-control parsley-validated" data-required="true"
                                                        value="{{ $dataList['whatsapp_phone_number_id'] }}">
                                                </div>
                                                <div class="form-group">
                                                    <label for="whatsapp_access_token">{{ __('Whatsapp Access Token') }}<span
                                                            class="red">*</span></label>
                                                    <textarea type="text" name="whatsapp_access_token" id="whatsapp_access_token"
                                                        class="form-control parsley-validated" data-required="true"
                                                        >{{ $dataList['whatsapp_access_token'] }}</textarea>
                                                </div>
                                            </div>
                                            <div class="col-lg-4"></div>
                                        </div>
                                        <div class="row tabs-footer mt-15">
                                            <div class="col-lg-12">
                                                <a id="global-setting-form" href="javascript:void(0);"
                                                    class="btn blue-btn">{{ __('Update') }}</a>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <!--/General Setting-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /main Section -->
@endsection

@push('scripts')
    <script src="{{ asset('backend/pages/settings.js') }}?v={{ time() }}"></script>
@endpush
