@extends('layouts.backend')

@section('title', __('Whatsapp Sent Message Logs'))

@section('content')
    <!-- main Section -->
    <div class="main-body">
        <div class="container-fluid">

            <div class="row mt-25">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row">
                                <div class="col-lg-12">
                                    <span>{{ __('Whatsapp Sent Message Logs') }}</span>
                                </div>
                            </div>
                        </div>

                        <!--Data grid-->
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-2">
                                    <div class="form-group bulk-box">
                                        <select id="sort_by" name="sort_by" class="form-control" onchange="onSearch()">
                                            <option selected value="DESC">{{ __('Sort By Newest') }}</option>
                                            <option value="ASC">{{ __('Sort By Oldest') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-2">
                                    <div class="form-group bulk-box">
                                        <input id="search" name="search" class="form-control" onchange="onSearch()"
                                            placeholder="Search by message ID or receiver">
                                    </div>
                                </div>
                                <div class="col-lg-2">
                                    <div class="form-group bulk-box">
                                        <button type="button" class="btn btn-primary" onclick="onRefreshData()"><i
                                                class="fa fa-refresh mr-1"></i>{{ __('Refresh') }}</button>
                                    </div>
                                </div>
                            </div>
                            <div id="tp_datalist" class="mt-2">
                                @include('backend.partials.sent_message_logs_table')
                            </div>
                        </div>
                        <!--/Data grid/-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /main Section -->

@endsection

@push('scripts')
    <!-- css/js -->
    <script type="text/javascript">
        var TEXT = [];
        TEXT['Do you really want to edit this record'] = "{{ __('Do you really want to edit this record') }}";
        TEXT['Do you really want to delete this record'] = "{{ __('Do you really want to delete this record') }}";
        TEXT['Do you really want to active this records'] = "{{ __('Do you really want to active this records') }}";
        TEXT['Do you really want to inactive this records'] = "{{ __('Do you really want to inactive this records') }}";
        TEXT['Do you really want to delete this records'] = "{{ __('Do you really want to delete this records') }}";
        TEXT['Please select action'] = "{{ __('Please select action') }}";
        TEXT['Please select record'] = "{{ __('Please select record') }}";
    </script>
    <script src="{{asset('backend/pages/whatsapp_sent_message_logs.js')}}?v={{time()}}"></script>
@endpush