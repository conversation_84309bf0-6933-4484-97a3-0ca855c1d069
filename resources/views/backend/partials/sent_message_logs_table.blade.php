<div class="table-responsive">
    <table class="table table-borderless table-theme" style="width:100%;border: 1px solid #dee2e6;">
        <thead>
            <tr>
                <th>Sr.</th>
                <th>Message ID</th>
                <th>Template</th>
                <th>Receiver</th>
                <th>Sent At</th>
                <th>Delivered At</th>
                <th>Read At</th>
                <th>Failed At</th>
                <th>Timestamp</th>
            </tr>
        </thead>
        <tbody>
            @foreach($dataList as $row)
                <tr>
                    <td>{{ $loop->index + 1 }}</td>
                    <td>{{ $row->message_id }}</td>
                    <td>{{ $row->template_name }}</td>
                    <td>{{ $row->receiver }}</td>
                    <td class="text-capitalize"><span class="badge badge-primary">{{ $row->sent_at ? date('d M, Y H:i:s', $row->sent_at) : 'Nil' }}</span></td>
                    <td class="text-capitalize"><span class="badge badge-success">{{ $row->delivered_at ? date('d M, Y H:i:s', $row->delivered_at) : 'Nil' }}</span></td>
                    <td class="text-capitalize"><span class="badge badge-info">{{ $row->read_at ? date('d M, Y H:i:s', $row->read_at) : 'Nil' }}</span></td>
                    <td class="text-capitalize"><span class="badge badge-danger">{{ $row->failed_at ? date('d M, Y H:i:s', $row->failed_at) : 'Nil' }}</span></td>
                    <td>{{ $row->created_at }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
</div>
<div class="row mt-15">
    <div class="col-lg-12 users_pagination">
        {{ $dataList->links('pagination.custom') }}
    </div>
</div>