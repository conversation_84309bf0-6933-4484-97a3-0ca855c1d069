<div class="table-responsive">
	<table class="table table-borderless table-theme" style="width:100%;border: 1px solid #dee2e6;">
		<thead> 
			<tr>
				<th>Sr.</th>
				<th>Message ID</th>
				<th>Whatsapp Account ID</th>
				<th>Phone Number ID</th>
				<th>Display Phone Number</th>
				<th>Recipient ID</th>
				<th>Category</th>
				<th>Status</th>
				<th>Error Code</th>
				<th>Error Title</th>
				<th>Error Message</th>
				<th>Error Details</th>
				<th>Timestamp</th>
			</tr>
		</thead> 
		<tbody>
			@foreach($dataList as $row)
			<tr>
				<td>{{ $loop->index + 1 }}</td>
				<td>{{ $row->message_id }}</td>
				<td>{{ $row->whatsapp_account_id }}</td>
				<td>{{ $row->phone_number_id }}</td>
				<td>{{ $row->display_phone_number }}</td>
				<td>{{ $row->recipient_id }}</td>
				<td>{{ Str::title($row->category) }}</td>
				@if($row->status == 'sent')
					<td class="text-capitalize"><span class="badge badge-primary">{{ $row->status }}</span></td>
				@elseif($row->status == 'delivered')
					<td class="text-capitalize"><span class="badge badge-success">{{ $row->status }}</span></td>
				@elseif($row->status == 'read')	
					<td class="text-capitalize"><span class="badge badge-info">{{ $row->status }}</span></td>
				@elseif($row->status == 'failed')	
					<td class="text-capitalize"><span class="badge badge-danger">{{ $row->status }}</span></td>
				@else
					<td class="text-capitalize"><span class="badge badge-warning">{{ $row->status }}</span></td>
				@endif
				<td>{{ $row->error_code ?? 'Nil' }}</td>
				<td>{{ $row->error_title ?? 'Nil' }}</td>
				<td>{{ $row->error_message ?? 'Nil' }}</td>
				<td>{{ $row->error_details ?? 'Nil' }}</td>
				<td>{{ date('Y-m-d H:i:s', $row->timestamp) }}</td>
			</tr>
			@endforeach
		</tbody>
	</table>
</div>
<div class="row mt-15">
	<div class="col-lg-12 users_pagination">
		{{ $dataList->links('pagination.custom') }}
	</div>
</div>