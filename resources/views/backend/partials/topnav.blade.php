<style>
	.notification-badge{
		position: absolute;
		right: -12px;
		top: -7px;
		padding: 4px 7px;
	}
</style>
@php
// $role_id =  Auth::user()->role_id;
// if(Auth::user()->role_id ==1){
// $admin_notifications_count 		=	DB::table('admin_notifications')->where('status','0')->get()->count();
// $admin_notifications 		=	DB::table('admin_notifications')->where('status','0')->take(10)->orderBy('id','DESC')->get()->all();


// }elseif(Auth::user()->role_id ==4){

// $admin_notifications_count 		=	DB::table('admin_notifications')->where('type',1)->where('status','0')->get()->count();
// $admin_notifications 		=	DB::table('admin_notifications')->where('type',1)->where('status','0')->take(10)->orderBy('id','DESC')->get()->all();
// $contact_notifications_count = [];$contact_notifications=[];
// $ads_notifications_count = [];$ads_notifications=[];
// $user_notifications_count = [];$user_notifications=[];

// }elseif(Auth::user()->role_id ==5){

// $admin_notifications_count 		=	DB::table('admin_notifications')->where('type',2)->where('status','0')->get()->count();
// $admin_notifications 		=	DB::table('admin_notifications')->where('type',2)->where('status','0')->take(10)->orderBy('id','DESC')->get()->all();
// $contact_notifications_count = [];$contact_notifications=[];
// $ads_notifications_count = [];$ads_notifications=[];
// $user_notifications_count = [];$user_notifications=[];

// }elseif(Auth::user()->role_id ==6){

// $admin_notifications_count 		=	DB::table('admin_notifications')->whereIn('type',[3,4])->where('status','0')->get()->count();
// $admin_notifications 		=	DB::table('admin_notifications')->whereIn('type',[3,4])->where('status','0')->take(10)->orderBy('id','DESC')->get()->all();
// $contact_notifications_count = [];$contact_notifications=[];
// $ads_notifications_count = [];$ads_notifications=[];
// $user_notifications_count = [];$user_notifications=[];

// }
@endphp
<nav class="navbar-expand-lg navbar tp-header">
	<span class="menu-toggler" id="menu-toggle">
		<span class="line"></span>
	</span>
	{{-- @if (Auth::user()->role_id == 1) --}}
	{{-- <a href="{{ url('/') }}" target="_blank" class="view_website">{{ __('View Website') }}</a> --}}
	{{-- @elseif (Auth::user()->role_id == 3)
	<a href="{{ route('frontend.stores', [Auth::user()->id, str_slug(Auth::user()->shop_url)]) }}" target="_blank" class="view_website">{{ __('View Your Store') }}</a>
	@endif --}}
	<div class="dropdown ml-auto mt-0 mt-lg-0">
		<a href="javascript:void(0);" class="my-profile-info" data-toggle="dropdown">
			<div class="avatar">
				<svg xmlns="" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-bell"><path d="M22 17H2a3 3 0 0 0 3-3V9a7 7 0 0 1 14 0v5a3 3 0 0 0 3 3zm-8.27 4a2 2 0 0 1-3.46 0"></path></svg>
				{{-- <span class="badge badge-pill badge-danger pull-right notification-badge">{{$admin_notifications_count}}</span> --}}
			</div>			
		</a> 
		<div class="dropdown-menu dropdown-menu-right my-profile-nav">
			<br>
			@php $n=0; @endphp
			{{-- @foreach ($admin_notifications  as $key=>$value)	@php $n++; @endphp		
			<a style="padding-left: 29px; @if(($n%2)==0) background: #f1f5f9; @endif" class="dropdown-item" href="{{ route('backend.getadminNotificationsView', [$value->id]) }}">{{$value->message}}</a>
			@endforeach	 --}}
			<br>
			{{-- <a class="dropdown-item" href="{{ route('backend.adminNotifications') }}" style="color: var(--backend-theme-color);text-align: center;background: #f1f5f9;">View all notification </a> --}}
		</div>
	</div>
	
	<div class="dropdown -ml-auto1 mt-0 mt-lg-0">
		<a href="javascript:void(0);" class="my-profile-info" data-toggle="dropdown">
			<div class="avatar">
				<img src="{{ Auth::user()->photo ? asset('media/'.Auth::user()->photo) : asset('backend/images/avatar.png') }}">
			</div>
			<div class="my-profile">
				<span>{{ Auth::user()->name }}</span>
				<span>{{ Auth::user()->email }}</span>
			</div>
		</a> 
		<div class="dropdown-menu dropdown-menu-right my-profile-nav">
			{{-- @if (Auth::user()->role_id == 1) --}}
			<a class="dropdown-item" href="{{ route('backend.profile') }}">{{ __('Profile') }}</a>
			{{-- @elseif (Auth::user()->role_id == 3)
			<a class="dropdown-item" href="{{ route('frontend.my-dashboard') }}">{{ __('My Account') }}</a>
			@endif --}}
			<a class="dropdown-item" href="{{ route('backend.logout') }}">
				{{ __('Logout') }}
			</a>
			<?php /*<a class="dropdown-item" href="{{ route('user.logout') }}"
				onclick="event.preventDefault();
				document.getElementById('logout-form').submit();">
				{{ __('Logout') }}
			</a>
			<form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
				@csrf
			</form> */ ?>
		</div>
	</div>
</nav>