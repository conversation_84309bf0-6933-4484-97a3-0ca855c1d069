<div class="table-responsive">
    <table class="table table-borderless table-theme" style="width:100%;border: 1px solid #dee2e6;">
        <thead>
            <tr>
                <th>Sr.</th>
                <th>Template Type</th>
                <th>Template Name</th>
                <th>Header Text</th>
                <th>Body Text</th>
                <th>Footer Text</th>
                <th>WhatsApp Template ID</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($templates as $template)
                <tr>
                    <td>{{ $loop->index + 1 }}</td>
                    <td>{{ $template->template_type }}</td>
                    <td>{{ $template->template_name }}</td>
                    <td>{{ $template->header_text ?? '-' }}</td>
                    <td>{{ Str::limit($template->body_text, 50) }}</td>
                    <td>{{ $template->footer_text ?? '-' }}</td>
                    <td>{{ $template->whatsapp_template_id ?? '-' }}</td>
                    <td>
                        <a href="javascript:void(0);" onClick="onEdit({{ $template->id }})"
                            class="btn btn-sm btn-primary">
                            <i class="fa fa-edit"></i> {{ __('Edit') }}
                        </a>
                        <a href="{{ route('backend.whatsapp-chatbot.templates.options.index', ['templateId' => $template->id]) }}" class="btn btn-sm btn-info">
                            <i class="fa fa-bars"></i> {{ __('Options') }}
                        </a>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
</div>
<div class="row mt-15">
    <div class="col-lg-12 users_pagination">
        {{ $templates->links('pagination.custom') }}
    </div>
</div>