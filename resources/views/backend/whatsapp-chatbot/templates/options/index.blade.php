
@extends('layouts.backend')

@section('title', __('Template Options'))

@section('content')
    <!-- main Section -->
    <div class="main-body">
        <div class="container-fluid">
            <div class="row mt-25">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row">
                                <div class="col-lg-6">
                                    <span>{{ __('Template Options for') }} {{ $template->template_name }}</span>
                                </div>
                                <div class="col-lg-6">
                                    <div class="float-right">
                                        <a onClick="onFormPanel()" href="javascript:void(0);"
                                            class="btn blue-btn btn-form float-right"><i class="fa fa-plus"></i>
                                            {{ __('Add New Option') }}</a>
                                        <a onClick="onListPanel()" href="javascript:void(0);"
                                            class="btn warning-btn btn-list float-right dnone"><i class="fa fa-reply"></i>
                                            {{ __('Back to List') }}</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Data grid -->
                        <div id="list-panel" class="card-body col-12">
                            <div class="row">
                                <div class="col-lg-4">
                                    <div class="form-group search-box">
                                        <input id="search" name="search" type="text" class="form-control"
                                            placeholder="{{ __('Search') }}...">
                                        <button type="submit" onClick="onSearch()"
                                            class="btn search-btn">{{ __('Search') }}</button>
                                    </div>
                                </div>
                            </div>
                            <div id="tp_datalist">
                                @include('backend.whatsapp-chatbot.templates.options.table')
                            </div>
                        </div>
                        <!-- /Data grid -->

                        <!-- Data Entry Form -->
                        <div id="form-panel" class="card-body dnone">
                            <form novalidate="" data-validate="parsley" id="DataEntry_formId">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="option_type">{{ __('Option Type') }}<span
                                                    class="red">*</span></label>
                                            <select name="option_type" id="option_type"
                                                class="form-control parsley-validated" data-required="true">
                                                <option value="BUTTON">{{ __('Button') }}</option>
                                                <option value="LIST_ITEM">{{ __('List Item') }}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="option_text">{{ __('Option Text') }}<span
                                                    class="red">*</span></label>
                                            <input type="text" name="option_text" id="option_text"
                                                class="form-control parsley-validated" data-required="true" maxlength="255">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="option_value">{{ __('Option Value') }}</label>
                                            <input type="text" name="option_value" id="option_value" class="form-control"
                                                maxlength="255">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="next_node_id">{{ __('Next Node') }}</label>
                                            <select name="next_node_id" id="next_node_id" class="form-control">
                                                <option value="">{{ __('Select Next Node') }}</option>
                                                @foreach ($nodes as $node)
                                                    <option value="{{ $node->id }}">{{ $node->message_text ?? $node->template->template_name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="order">{{ __('Order') }}</label>
                                            <input type="number" name="order" id="order" class="form-control" min="0">
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" name="option_id" id="option_id" class="dnone">
                                <input type="hidden" name="template_id" id="template_id" value="{{ $template->id }}">
                                <div class="row tabs-footer mt-15">
                                    <div class="col-lg-12">
                                        <a id="submit-form" href="javascript:void(0);"
                                            class="btn blue-btn mr-10">{{ __('Save') }}</a>
                                        <a onClick="onListPanel()" href="javascript:void(0);"
                                            class="btn danger-btn">{{ __('Cancel') }}</a>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <!-- /Data Entry Form -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /main Section -->
@endsection

@push('scripts')
    <script type="text/javascript">
        var media_type = 'Template_Option';
        var TEXT = [];
        TEXT['Do you really want to edit this record'] = "{{ __('Do you really want to edit this record') }}";
        TEXT['Do you really want to delete this record'] = "{{ __('Do you really want to delete this record') }}";
    </script>
    <script src="{{ asset('backend/pages/whatsapp-template-options.js') }}?v={{ time() }}"></script>
@endpush