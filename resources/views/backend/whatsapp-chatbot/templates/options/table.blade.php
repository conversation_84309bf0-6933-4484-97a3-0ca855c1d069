<div class="table-responsive">
    <table class="table table-borderless table-theme" style="width:100%;border: 1px solid #dee2e6;">
        <thead>
        <tr>
            <th>{{ __('Option ID') }}</th>
            <th>{{ __('Option Type') }}</th>
            <th>{{ __('Option Text') }}</th>
            <th>{{ __('Option Value') }}</th>
            <th>{{ __('Order') }}</th>
            <th>{{ __('Next Node ID') }}</th>
            <th>{{ __('Actions') }}</th>
        </tr>
    </thead>
    <tbody>
        @foreach ($options as $option)
            <tr>
                <td>{{ $option->option_id }}</td>
                <td>{{ $option->option_type }}</td>
                <td>{{ $option->option_text }}</td>
                <td>{{ $option->option_value ?? '-' }}</td>
                <td>{{ $option->order ?? '-' }}</td>
                <td>{{ $option->next_node_id ?? '-' }}</td>
                <td>
                    <a href="javascript:void(0);" onClick="onEdit({{ $option->option_id }})" class="btn btn-sm btn-primary">
                        <i class="fa fa-edit"></i> {{ __('Edit') }}
                    </a>
                    <a href="javascript:void(0);" onClick="onDelete({{ $option->option_id }})"
                        class="btn btn-sm btn-danger">
                        <i class="fa fa-trash"></i> {{ __('Delete') }}
                    </a>
                </td>
            </tr>
        @endforeach
    </tbody>
</table>
<div class="row mt-15">
    <div class="col-lg-12 users_pagination">
        {{ $options->links('pagination.custom') }}
    </div>
</div>