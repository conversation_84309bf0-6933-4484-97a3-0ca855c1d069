@extends('layouts.backend')

@section('title', __('Interactive Templates'))

@section('content')
    <!-- main Section -->
    <div class="main-body">
        <div class="container-fluid">
            <div class="row mt-25">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row">
                                <div class="col-lg-6">
                                    <span>{{ __('Interactive Templates') }}</span>
                                </div>
                                <div class="col-lg-6">
                                    <div class="float-right">
                                        <a onClick="onFormPanel()" href="javascript:void(0);"
                                            class="btn blue-btn btn-form float-right"><i class="fa fa-plus"></i>
                                            {{ __('Add New Template') }}</a>
                                        <a onClick="onListPanel()" href="javascript:void(0);"
                                            class="btn warning-btn btn-list float-right dnone"><i class="fa fa-reply"></i>
                                            {{ __('Back to List') }}</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Data grid -->
                        <div id="list-panel" class="card-body col-12">
                            <div class="row">
                                <div class="col-lg-4">
                                    <div class="form-group search-box">
                                        <input id="search" name="search" type="text" class="form-control"
                                            placeholder="{{ __('Search') }}...">
                                        <button type="submit" onClick="onSearch()"
                                            class="btn search-btn">{{ __('Search') }}</button>
                                    </div>
                                </div>
                            </div>
                            <div id="tp_datalist">
                                @include('backend.whatsapp-chatbot.templates.table')
                            </div>
                        </div>
                        <!-- /Data grid -->

                        <!-- Data Entry Form -->
                        <div id="form-panel" class="card-body dnone">
                            <form novalidate="" data-validate="parsley" id="DataEntry_formId">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="template_type">{{ __('Template Type') }}<span
                                                    class="red">*</span></label>
                                            <select name="template_type" id="template_type"
                                                class="form-control parsley-validated" data-required="true">
                                                <option value="BUTTON">{{ __('Button') }}</option>
                                                <option value="LIST">{{ __('List') }}</option>
                                                <option value="PRODUCT">{{ __('Product') }}</option>
                                                <option value="CUSTOM">{{ __('Custom') }}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="template_name">{{ __('Template Name') }}<span
                                                    class="red">*</span></label>
                                            <input type="text" name="template_name" id="template_name"
                                                class="form-control parsley-validated" data-required="true" maxlength="100">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="whatsapp_template_id">{{ __('WhatsApp Template ID') }}</label>
                                            <input type="text" name="whatsapp_template_id" id="whatsapp_template_id"
                                                class="form-control" maxlength="100">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="header_text">{{ __('Header Text') }}</label>
                                            <input type="text" name="header_text" id="header_text" class="form-control"
                                                maxlength="255">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="body_text">{{ __('Body Text') }}<span class="red">*</span></label>
                                            <textarea name="body_text" id="body_text" class="form-control parsley-validated"
                                                data-required="true"></textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="footer_text">{{ __('Footer Text') }}</label>
                                            <input type="text" name="footer_text" id="footer_text" class="form-control"
                                                maxlength="255">
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" name="template_id" id="template_id" class="dnone">
                                <input type="hidden" name="chatbot_id" id="chatbot_id" value="{{ $chatBotId }}">
                                <div class="row tabs-footer mt-15">
                                    <div class="col-lg-12">
                                        <a id="submit-form" href="javascript:void(0);"
                                            class="btn blue-btn mr-10">{{ __('Save') }}</a>
                                        <a onClick="onListPanel()" href="javascript:void(0);"
                                            class="btn danger-btn">{{ __('Cancel') }}</a>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <!-- /Data Entry Form -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /main Section -->
@endsection

@push('scripts')
    <script type="text/javascript">
        var media_type = 'Interactive_Template';
        var TEXT = [];
        TEXT['Do you really want to edit this record'] = "{{ __('Do you really want to edit this record') }}";
        TEXT['Do you really want to delete this record'] = "{{ __('Do you really want to delete this record') }}";
    </script>
    <script src="{{ asset('backend/pages/whatsapp-chatbot-templates.js') }}?v={{ time() }}"></script>
@endpush