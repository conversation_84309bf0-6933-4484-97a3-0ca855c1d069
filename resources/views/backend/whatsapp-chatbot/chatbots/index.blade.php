@extends('layouts.backend')

@section('title', __('Chatbots'))

@section('content')
    <!-- main Section -->
    <div class="main-body">
        <div class="container-fluid">


            <div class="row mt-25">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row">
                                <div class="col-lg-6">
                                    <span>{{ __(' Chatbots') }}</span>
                                </div>
                                <div class="col-lg-6">
                                    <div class="float-right">
                                        <a onClick="onFormPanel()" href="javascript:void(0);"
                                            class="btn blue-btn btn-form float-right"><i class="fa fa-plus"></i>
                                            {{ __('Add New') }}</a>
                                        <a onClick="onListPanel()" href="javascript:void(0);"
                                            class="btn warning-btn btn-list float-right dnone"><i class="fa fa-reply"></i>
                                            {{ __('Back to List') }}</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!--Data grid-->
                        <div id="list-panel" class="card-body col-12">
                            <div class="row">
                                <div class="col-lg-4">
                                    <div class="form-group search-box">
                                        <input id="search" name="search" type="text" class="form-control"
                                            placeholder="{{ __('Search') }}...">
                                        <button type="submit" onClick="onSearch()"
                                            class="btn search-btn">{{ __('Search') }}</button>
                                    </div>
                                </div>
                            </div>
                            <div id="tp_datalist">
                                @include('backend.whatsapp-chatbot.chatbots.table')
                            </div>
                        </div>
                        <!--/Data grid/-->
                        <!--Data Entry Form-->
                        <div id="form-panel" class="card-body dnone">
                            <form novalidate="" data-validate="parsley" id="DataEntry_formId">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="name">{{ __('Chatbot Name') }}<span class="red">*</span></label>
                                            <input type="text" name="name" id="name" class="form-control parsley-validated"
                                                data-required="true">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="description">{{ __('Chatbot Description') }}<span
                                                    class="red">*</span></label>
                                            <input type="text" name="description" id="name_ar"
                                                class="form-control parsley-validated" data-required="true">
                                        </div>
                                    </div>
                                </div>
                                <input type="text" name="RecordId" id="RecordId" class="dnone">
                                <div class="row tabs-footer mt-15">
                                    <div class="col-lg-12">
                                        <a id="submit-form" href="javascript:void(0);"
                                            class="btn blue-btn mr-10">{{ __('Save') }}</a>
                                        <a onClick="onListPanel()" href="javascript:void(0);"
                                            class="btn danger-btn">{{ __('Cancel') }}</a>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <!--/Data Entry Form/-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /main Section -->

@endsection

@push('scripts')
    <!-- css/js -->
    <script type="text/javascript">
        var media_type = 'Product_Thumbnail';
        var TEXT = [];
        TEXT['Do you really want to edit this record'] = "{{ __('Do you really want to edit this record') }}";
        TEXT['Do you really want to delete this record'] = "{{ __('Do you really want to delete this record') }}";
        TEXT['Do you really want to publish this records'] = "{{ __('Do you really want to publish this records') }}";
        TEXT['Do you really want to draft this records'] = "{{ __('Do you really want to draft this records') }}";
        TEXT['Do you really want to delete this records'] = "{{ __('Do you really want to delete this records') }}";
        TEXT['Please select action'] = "{{ __('Please select action') }}";
        TEXT['Please select record'] = "{{ __('Please select record') }}";
    </script>
    <script src="{{ asset('backend/pages/whatsapp-chatbots.js') }}?v={{ time() }}"></script>
@endpush