<div class="table-responsive">
    <table class="table table-borderless table-theme" style="width:100%;border: 1px solid #dee2e6;">
        <thead>
            <tr>
                <th>Sr.</th>
                <th>Name</th>
                <th>Description</th>
                <th>Created At</th>
                <th>Update At</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            @foreach($dataList as $row)
                <tr>
                    <td>{{ $loop->index + 1 }}</td>
                    <td>{{ $row->name }}</td>
                    <td>{{ $row->description }}</td>
                    <td>{{ $row->created_at }}</td>
                    <td>{{ $row->updated_at }}</td>
                    <td>
                        <a href="javascript:void(0);" class="btn btn-primary" onclick="onEdit({{ $row->id }})">
                            <i class="fa fa-edit"></i>
                            {{ __('Edit') }}
                        </a>
                        <a href="{{ route('backend.whatsapp-chatbot.templates.index', ['chatBotId' => $row->id]) }}" class="btn btn-info">
                            <i class="fa fa-cogs"></i>
                            {{ __('Interactive Templates') }}
                        </a>
                        <a href="{{ route('backend.whatsapp-chatbot.chat-nodes', ['chatBotId' => $row->id]) }}" class="btn btn-info">
                            <i class="fa fa-sitemap"></i>
                            {{ __('Nodes') }}
                        </a>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
</div>
<div class="row mt-15">
    <div class="col-lg-12 users_pagination">
        {{ $dataList->links('pagination.custom') }}
    </div>
</div>

<!-- Chat Nodes Modal -->
<div class="modal fade" id="chatNodesModal" tabindex="-1" aria-labelledby="chatNodesModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="chatNodesModalLabel">Chatbot Nodes</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" id="chatNodesContent">
        <!-- Nodes will be loaded here -->
      </div>
    </div>
  </div>
</div>

@push('scripts')
@endpush