<div class="table-responsive">
    <table class="table table-borderless table-theme" style="width:100%;border: 1px solid #dee2e6;">
        <thead>
            <tr>
                <th>{{ __('Node ID') }}</th>
                <th>{{ __('Node Type') }}</th>
                <th>{{ __('Message Text') }}</th>
                <th>{{ __('Template ID') }}</th>
                <th>{{ __('Parent Node') }}</th>
                <th>{{ __('Is Root') }}</th>
                <th>{{ __('Actions') }}</th>
                </tr>
        </thead>
        <tbody>
            @foreach($nodes as $row)
                <tr>
                    <td>{{ $node->node_id }}</td>
                    <td>{{ $node->node_type }}</td>
                    <td>{{ $node->message_text ?? '-' }}</td>
                    <td>{{ $node->template_id ?? '-' }}</td>
                    <td>{{ $node->parent ? $node->parent->node_id : '-' }}</td>
                    <td>{{ $node->is_root ? 'Yes' : 'No' }}</td>
                    <td>
                        <a href="javascript:void(0);" onClick="onEdit({{ $node->node_id }})" class="btn btn-sm btn-primary">
                            <i class="fa fa-edit"></i> {{ __('Edit') }}
                        </a>
                        <a hrefジャック="javascript:void(0);" onClick="onDelete({{ $node->node_id }})" class="btn btn-sm btn-danger">
                            <i class="fa fa-trash"></i> {{ __('Delete') }}
                        </a>
                        </td>
                </tr>
            @endforeach
        </tbody>
    </table>
</div>
<div class="row mt-15">
    <div class="col-lg-12 users_pagination">
        {{ $nodes->links('pagination.custom') }}
    </div>
</div>