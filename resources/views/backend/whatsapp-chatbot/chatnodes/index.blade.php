@extends('layouts.backend')

@section('title', __('Chatbot Nodes'))

@section('content')
    <!-- main Section -->
    <div class="main-body">
        <div class="container-fluid">
            <div class="row mt-25">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row">
                                <div class="col-lg-6">
                                    <span>{{ __('Chatbot Nodes') }}</span>
                                </div>
                                <div class="col-lg-6">
                                    <div class="float-right">
                                        <a onClick="onFormPanel()" href="javascript:void(0);"
                                            class="btn blue-btn btn-form float-right"><i class="fa fa-plus"></i>
                                            {{ __('Add New Node') }}</a>
                                        <a onClick="onListPanel()" href="javascript:void(0);"
                                            class="btn warning-btn btn-list float-right dnone"><i class="fa fa-reply"></i>
                                            {{ __('Back to List') }}</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Data grid -->
                        <div id="list-panel" class="card-body col-12">
                            <div class="row">
                                <div class="col-lg-4">
                                    <div class="form-group search-box">
                                        <input id="search" name="search" type="text" class="form-control"
                                            placeholder="{{ __('Search') }}...">
                                        <button type="submit" onClick="onSearch()"
                                            class="btn search-btn">{{ __('Search') }}</button>
                                    </div>
                                </div>
                            </div>
                            <div id="tp_datalist">
                                @include('backend.whatsapp-chatbot.chatnodes.table')
                            </div>
                        </div>
                        <!-- /Data grid -->

                        <!-- Data Entry Form -->
                        <div id="form-panel" class="card-body dnone">
                            <form novalidate="" data-validate="parsley" id="DataEntry_formId">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="node_type">{{ __('Node Type') }}<span class="red">*</span></label>
                                            <select name="node_type" id="node_type" class="form-control parsley-validated"
                                                data-required="true">
                                                <option value="MESSAGE">{{ __('Message') }}</option>
                                                <option value="QUESTION">{{ __('Question') }}</option>
                                                <option value="ACTION">{{ __('Action') }}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="message_text">{{ __('Message Text') }}</label>
                                            <input type="text" name="message_text" id="message_text"
                                                class="form-control parsley-validated" data-required="true"
                                                data-required-if="node_type:MESSAGE">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="template_id">{{ __('Template') }}</label>
                                            <select name="template_id" id="template_id"
                                                class="form-control parsley-validated" data-required="true"
                                                data-required-if="node_type:QUESTION">
                                                <option value="">{{ __('Select Template') }}</option>
                                                @foreach ($templates as $template)
                                                    <option value="{{ $template->template_id }}">{{ $template->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="parent_node_id">{{ __('Parent Node') }}</label>
                                            <select name="parent_node_id" id="parent_node_id" class="form-control">
                                                <option value="">{{ __('Select Parent Node') }}</option>
                                                @foreach ($nodes as $node)
                                                    <option value="{{ $node->node_id }}">{{ $node->node_id }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="is_root">{{ __('Is Root Node') }}</label>
                                            <input type="checkbox" name="is_root" id="is_root" value="1">
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" name="node_id" id="node_id" class="dnone">
                                <input type="hidden" name="chatbot_id" id="chatbot_id" value="{{ $chatBotId }}">
                                <div class="row tabs-footer mt-15">
                                    <div class="col-lg-12">
                                        <a id="submit-form" href="javascript:void(0);"
                                            class="btn blue-btn mr-10">{{ __('Save') }}</a>
                                        <a onClick="onListPanel()" href="javascript:void(0);"
                                            class="btn danger-btn">{{ __('Cancel') }}</a>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <!-- /Data Entry Form -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /main Section -->
@endsection

@push('scripts')
    <script type="text/javascript">
        var media_type = 'Chatbot_Node';
        var TEXT = [];
        TEXT['Do you really want to edit this record'] = "{{ __('Do you really want to edit this record') }}";
        TEXT['Do you really want to delete this record'] = "{{ __('Do you really want to delete this record') }}";
    </script>
    <script src="{{ asset('backend/pages/whatsapp-chat-nodes.js') }}?v={{ time() }}"></script>
@endpush