@extends('layouts.backend')

@section('title', __('Profile'))

@section('content')
    <!-- Main Section -->
    <div class="main-body">
        <div class="container-fluid">
            <div class="row mt-25">
                <div class="col-lg-12">
                    <!-- Profile Card -->
                    <div class="card">
                        <div class="card-header">{{ __('Profile') }}</div>
                        <div class="card-body">
                            <form novalidate data-parsley-validate id="profileForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="name">{{ __('Name') }} <span class="text-danger">*</span></label>
                                            <input type="text" name="name" id="name" class="form-control" value=""
                                                data-parsley-required="true">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="email">{{ __('Email Address') }} <span
                                                    class="text-danger">*</span></label>
                                            <input type="email" name="email" id="email" class="form-control" value=""
                                                data-parsley-required="true">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="phone">{{ __('Phone') }}</label>
                                            <input type="text" name="phone" id="phone" class="form-control" value="">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="photo_thumbnail">{{ __('Profile Photo') }}</label>
                                            <div class="tp-upload-field">
                                                <input type="text" name="photo" id="photo_thumbnail" class="form-control"
                                                    value="" readonly>
                                                <a id="on_thumbnail" href="javascript:void(0);" class="tp-upload-btn">
                                                    <i class="fa fa-window-restore"></i> {{ __('Browse') }}
                                                </a>
                                            </div>
                                            <em>Recommended image size: 200px x 200px</em>
                                            <div id="remove_photo_thumbnail" class="select-image" style="display: none;">
                                                <div class="inner-image" id="view_photo_thumbnail"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <input type="hidden" id="RecordId" name="RecordId">

                                <div class="row tabs-footer mt-15">
                                    <div class="col-lg-12">
                                        <button type="submit" id="submitProfile"
                                            class="btn blue-btn">{{ __('Save') }}</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Change Password Card -->
                    <div class="card">
                        <div class="card-header">{{ __('Change Password') }}</div>
                        <div class="card-body">
                            <form novalidate data-parsley-validate id="change-password-form">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group position-relative">
                                            <label for="new_password">{{ __('New Password') }} <span
                                                    class="text-danger">*</span></label>
                                            <span toggle="#new_password" class="fa fa-eye field-icon toggle-password"
                                                aria-label="Toggle password visibility"></span>
                                            <input type="password" maxlength="12" minlength="6" name="password"
                                                id="new_password" class="form-control" data-parsley-required="true">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group position-relative">
                                            <label for="confirm_password">{{ __('Confirm Password') }} <span
                                                    class="text-danger">*</span></label>
                                            <span toggle="#confirm_password" class="fa fa-eye field-icon toggle-password"
                                                aria-label="Toggle password visibility"></span>
                                            <input type="password" maxlength="12" minlength="6" name="confirm_password"
                                                id="confirm_password" class="form-control" data-parsley-required="true"
                                                data-parsley-equalto="#new_password">
                                        </div>
                                    </div>
                                </div>

                                <div class="row tabs-footer mt-15">
                                    <div class="col-lg-12">
                                        <button type="submit" id="submitPassword"
                                            class="btn blue-btn">{{ __('Save') }}</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /Main Section -->

    <!-- Global Media -->
    @include('backend.partials.global_media')
    <!-- /Global Media -->
@endsection

@push('scripts')
    <script>
        var media_type = 'Thumbnail';
        var userid = "{{ Auth::user()->id }}";
        var public_path = "{{ asset('') }}"; // Ensure public path is available
    </script>
    <script src="{{ asset('backend/pages/profile.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('backend/pages/global-media.js') }}?v={{ time() }}"></script>
@endpush