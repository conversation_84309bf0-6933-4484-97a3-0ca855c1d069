@extends('layouts.backend')

@section('title', __('Whatsapp Templates'))

@section('content')
    <!-- main Section -->
    <div class="main-body">
        <div class="container-fluid">
            <div class="row mt-25">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row">
                                <div class="col-lg-12">
                                    <span>{{ __('Whatsapp Templates') }}</span>
                                </div>
                            </div>
                        </div>
                        <!--Data Entry Form-->
                        <div id="form-panel" class="card-body">
                            <form novalidate data-parsley-validate id="DataEntry_formId" autocomplete="off">
                                @csrf
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="template_id">{{ __('Template') }}<span class="red">*</span></label>
                                            <select name="template_id" id="template_id" required class="form-control select2"
                                                onchange="fetchTemplateData(this)">
                                                <option value="">{{ __('Select Template') }}</option>
                                                @foreach ($templates as $template)
                                                    <option value="{{ $template->id }}" data-slug="{{ $template->slug }}">
                                                        {{ $template->name }} ({{ $template->language }})</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="product_id">{{ __('Product') }}<span class="red">*</span></label>
                                            <select name="product_id" id="product_id" class="form-control select2">
                                                <option value="">{{ __('Select Product') }}</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div id="dynamic-fields" class="row">
                                    <!-- Dynamic fields will be injected here -->
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="slider_image">{{ __('Image') }}</label>
                                            <div class="tp-upload-field">
                                                <input type="text" name="image" id="slider_image" class="form-control">
                                                <a onClick="onGlobalMediaModalView()" href="javascript:void(0);"
                                                    class="tp-upload-btn"><i
                                                        class="fa fa-window-restore"></i>{{ __('Browse') }}</a>
                                            </div>
                                            <div id="remove_slider_image" class="select-image">
                                                <div class="inner-image" id="view_slider_image"></div>
                                                <a onClick="onMediaImageRemove('slider_image','eng')"
                                                    class="media-image-remove" href="javascript:void(0);"><i
                                                        class="fa fa-remove"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row tabs-footer mt-15">
                                    <div class="col-lg-12">
                                        <button type="submit" id="send-message" class="btn blue-btn">{{ __('Send') }}</button>
                                        <a onClick="onListPanel()" href="javascript:void(0);"
                                            class="btn danger-btn">{{ __('Cancel') }}</a>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <!--/Data Entry Form/-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /main Section -->
    <!--Global Media-->
    @include('backend.partials.global_media')
    <!--/Global Media/-->
@endsection

@push('scripts')
    <!-- css/js -->
    <script type="text/javascript">
        var media_type = 'Brand';
        var TEXT = [];
        TEXT['Do you really want to edit this record'] = "{{ __('Do you really want to edit this record') }}";
        TEXT['Do you really want to delete this record'] = "{{ __('Do you really want to delete this record') }}";
        TEXT['Do you really want to publish this records'] = "{{ __('Do you really want to publish this records') }}";
        TEXT['Do you really want to draft this records'] = "{{ __('Do you really want to draft this records') }}";
        TEXT['Do you really want to delete this records'] = "{{ __('Do you really want to delete this records') }}";
        TEXT['Please select action'] = "{{ __('Please select action') }}";
        TEXT['Please select record'] = "{{ __('Please select record') }}";
    </script>
    <script src="{{ asset('backend/pages/whatsapp_templates.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('backend/pages/global-media.js') }}?v={{ time() }}"></script>

@endpush