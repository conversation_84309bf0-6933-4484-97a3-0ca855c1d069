@if ($paginator->hasPages())
<nav>
    <ul class="pagination justify-content-center1">
        @if ($paginator->onFirstPage())
            <li class="disabled  page-item "><span class="page-link">  <</span></li>
        @else
            <li class="page-item"><a href="{{ $paginator->previousPageUrl() }}" rel="prev"  class="page-link page-link-un">  <</a></li>
        @endif
        <?php  //echo "<pre>"; print_R($elements);  echo "</pre>";  ?>
        @foreach ($elements as $element)
            @if (is_string($element))
                <li class="disabled page-item 111"><span  class="page-link">{{ $element }}</span></li>
            @endif
            @if (is_array($element))
                @foreach ($element as $page => $url)
                    @if ($page == $paginator->currentPage())
                        <li class="page-item active"><span  class="page-link">{{ $page }}</span></li>
                    @else
                        <li class="page-item"><a href="{{ $url }}"  class="page-link page-link-un">{{ $page }}</a></li>
                    @endif
                @endforeach
            @endif
        @endforeach
        @if ($paginator->hasMorePages())
            <li class="page-item"><a href="{{ $paginator->nextPageUrl() }}" rel="next"  class="page-link page-link-un">></a></li>
        @else
            <li class="disabled page-item"><span  class="page-link">></span></li>
        @endif
    </ul>
</nav>
@endif 