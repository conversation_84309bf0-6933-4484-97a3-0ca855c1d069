<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
  <head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
	<title>@yield('title') | {{ config('app.name') }}</title>
	<!-- favicon -->
	<link rel="shortcut icon" href="{{ config('app.favicon') ? asset('media/'.config('app.favicon')) : asset('backend/images/favicon.ico') }}" type="image/x-icon">
	<link rel="icon" href="{{ config('app.favicon') ? asset('media/'.config('app.favicon')) : asset('backend/images/favicon.ico') }}" type="image/x-icon">
    <!-- CSS -->
	<style type="text/css">
	:root {
	  --backend-theme-color: {{ config('app.theme_color') }};
	}
	</style>
    <link rel="stylesheet" href="{{asset('backend/css/bootstrap.min.css')}}">
    <link rel="stylesheet" href="{{asset('backend/css/font-awesome.min.css')}}">
    <link rel="stylesheet" href="{{asset('backend/css/style.css')}}">
    <link rel="stylesheet" href="{{asset('backend/css/responsive.css')}}">
	@stack('style')
  </head>
  <body>
	@yield('content')
    <!-- JS -->
	<script src="{{asset('backend/js/jquery-3.6.0.min.js')}}"></script>
	<script src="{{asset('backend/js/popper.min.js')}}"></script>
	<script src="{{asset('backend/js/bootstrap.min.js')}}"></script>
	@stack('scripts')
  </body>
</html>