<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title') | {{ config('app.name') }}</title>
    <!-- favicon -->
    <link rel="shortcut icon"
        href="{{ config('app.favicon') ? asset('media/'.config('app.favicon')) : asset('backend/images/favicon.ico') }}"
        type="image/x-icon">
    <link rel="icon"
        href="{{ config('app.favicon') ? asset('media/'.config('app.favicon')) : asset('backend/images/favicon.ico') }}"
        type="image/x-icon">
    <!-- CSS -->
    <style type="text/css">
        :root {
            --backend-theme-color: {{ config('app.theme_color') }};
        }
    </style>

    <link rel="stylesheet" href="https://code.jquery.com/mobile/1.4.5/jquery.mobile-1.4.5.min.css" />

    <link rel="stylesheet" href="{{ asset('backend/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('backend/css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{ asset('backend/css/chosen/bootstrap-chosen.min.css') }}">
    <link rel="stylesheet" href="{{ asset('backend/css/jquery.gritter.min.css') }}">
    <link rel="stylesheet" href="{{ asset('backend/css/select2.min.css') }}">
    <link rel="stylesheet" href="{{ asset('backend/css/style.css') }}">
    <link rel="stylesheet" href="{{ asset('backend/css/responsive.css') }}">
    <script src="{{ asset('backend/js/jquery-3.6.0.min.js') }}"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    @stack('style')
    <style>
        .sidebar-wrapper ul.left-navbar li.unActive a.has-dropdown {
            color: #fff;
            background-color: var(--backend-theme-color);
        }

        .unActive1 a {
            color: var(--backend-theme-color) !important;
            background-color: transparent;
        }

        .unActive1 a:hover {
            color: #fff !important;
        }

        .un-loader {
            background: rgba(0, 0, 0, 0.5);
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 999;
            display: none;
        }

        .un-loader img {
            position: absolute;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
            -ms-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
        }

        .unFilter .search-box input.form-control,
        .unFilter .bulk-box select.form-control {
            padding: 10px 15px !important;
        }

        .unFilter .search-box .search-btn,
        .unFilter .bulk-box .bulk-btn {
            padding: 13px !important;
        }

        .pac-container {
            z-index: 10000000 !important;
        }
    </style>
</head>

<body>
    <div id="wrapper" class="d-flex relative">
        <!-- Loader Start -->

        <div class="un-loader">
            <img src="{{ url('/media/un-loader.svg') }}" alt="">
        </div>
        <!-- Loader End -->
        <!-- Sidebar -->
        @include('backend.partials.sidebar')
        <!-- /Sidebar/ -->
        <!-- Page Content -->
        <div id="page-content-wrapper">
            <!--Top Navbar-->
            @include('backend.partials.topnav')
            <!--/Top Navbar/-->
            <!--Main Body-->
            @yield('content')
            <!--/Main Body/-->
        </div><!-- /Page Content/ -->
    </div><!--/wrapper-->
    <!-- JS -->
    <script src="{{ asset('backend/js/popper.min.js') }}"></script>
    <script src="{{ asset('backend/js/bootstrap.min.js') }}"></script>
    <script src="{{ asset('backend/js/jquery-ui.min.js') }}"></script>
    <script src="{{ asset('backend/js/jquery.nicescroll.min.js') }}"></script>
    <script src="{{ asset('backend/js/parsley.min.js') }}"></script>
    <script src="{{ asset('backend/js/chosen.jquery.min.js') }}"></script>
    <script src="{{ asset('backend/js/jquery.popupoverlay.min.js') }}"></script>
    <script src="{{ asset('backend/js/jquery.gritter.min.js') }}"></script>
    <script src="{{ asset('backend/js/select2.min.js') }}"></script>
    <script src="{{url('/')}}/assets/js/chart.js"></script>
    <script src="{{url('/')}}/assets/js/jquery.blockUI.js"></script>
    <script type="text/javascript">
        var base_url = "{{ url('/') }}";
        var public_path = "{{ url('/') }}";
        var userid = "{{ Auth::user()->id }}";
    </script>
    <script src="{{ asset('backend/js/script.js') }}"></script>
    <div class="custom-popup light width-100 dnone" id="lightCustomModal">
        <div class="padding-md">
            <h4 class="m-top-none">{{ __('This is alert message') }}</h4>
        </div>
        <div class="text-center">
            <a href="javascript:void(0);" class="btn blue-btn lightCustomModal_close mr-10"
                onClick="onConfirm()">{{ __('Confirm') }}</a>
            <a href="javascript:void(0);" class="btn danger-btn lightCustomModal_close">{{ __('Cancel') }}</a>
        </div>
    </div>
    <a href="#lightCustomModal" class="btn btn-warning btn-small lightCustomModal_open dnone">{{ __('Edit') }}</a>
    @stack('scripts')
</body>

</html>
<script src="https://code.jquery.com/mobile/1.4.5/jquery.mobile-1.4.5.min.js"></script>


<script>
    function loadingShow() {
        $(".un-loader").show();
    }

    function loadingHide() {
        $(".un-loader").hide();
    }


    var $loading = $('#loading').hide();
    //Attach the event handler to any element
    // $(document)
    //     .ajaxStart(function() {
    //         //ajax request went so show the loading image
    //         loadingShow();
    //     })
    //     .ajaxStop(function() {
    //         //got response so hide the loading image
    //         loadingHide();
    //     });
    // <script>
   // Global AJAX event handlers for loading indicators
    $(document)
            .ajaxStart(function () {
                // Show BlockUI when any AJAX request starts
                $.blockUI({
                    message: '<div class="d-flex justify-content-center align-items-center"><div class="spinner-border" style="color: var(--backend-theme-color)" role="status"></div><span class="ms-3 text-gray-700">&nbsp;&nbsp;Working on it...</span></div>',
                    css: {
                        border: 'none',
                        padding: '15px',
                        backgroundColor: '#ffffff',
                        '-webkit-border-radius': '10px',
                        '-moz-border-radius': '10px',
                        'border-radius': '10px',
                        opacity: 0.8,
                        color: '#000000'
                    },
                    overlayCSS: {
                        backgroundColor: '#000000',
                        opacity: 0.6,
                        zIndex: 9999
                    }
                });
            })
            .ajaxStop(function () {
                // Hide BlockUI when all AJAX requests complete
                $.unblockUI();
            });
    $(".left-navbar li").mouseover(function() {
        if (!$(this).hasClass('active')) {
            var classD = $(this).attr('data-id');
            if (classD == 'delivery') {
                $('.savColor_' + classD + ' #secondary').attr('fill', '#FFF');
                $('.savColor_' + classD + ' #primary').attr('stroke', '#FFF');
                $('.savColor_' + classD + ' #primary-2').attr('stroke', '#FFF');
                $('.savColor_' + classD + ' #primary-3').attr('stroke', '#FFF');
                $('.savColor_' + classD + ' #primary-4').attr('stroke', '#FFF');
            } else {
                $('.savColor_' + classD).attr('fill', '#FFF');
            }

        }
    });
    $(".left-navbar li").mouseleave(function() {
        if (!$(this).hasClass('active')) {
            var classD = $(this).attr('data-id');
            if (classD == 'delivery') {
                $('.savColor_' + classD + ' #secondary').attr('fill', '#808182');
                $('.savColor_' + classD + ' #primary').attr('stroke', '#808182');
                $('.savColor_' + classD + ' #primary-2').attr('stroke', '#808182');
                $('.savColor_' + classD + ' #primary-3').attr('stroke', '#808182');
                $('.savColor_' + classD + ' #primary-4').attr('stroke', '#808182');
            } else {
                $('.savColor_' + classD).attr('fill', '#808182');
            }
        }
    });
    $(".left-navbar li.active").each(function() {
        var classD = $(this).attr('data-id');
        if (classD != 'undefined' && classD != undefined) {
            if (classD == 'delivery') {
                $('.savColor_' + classD + ' #secondary').attr('fill', '#FFF');
                $('.savColor_' + classD + ' #primary').attr('stroke', '#FFF');
                $('.savColor_' + classD + ' #primary-2').attr('stroke', '#FFF');
                $('.savColor_' + classD + ' #primary-3').attr('stroke', '#FFF');
                $('.savColor_' + classD + ' #primary-4').attr('stroke', '#FFF');
            } else {
                $('.savColor_' + classD).attr('fill', '#FFF');
            }
        }
    });
    $(".left-navbar li.unActive").each(function() {
        var classD = $(this).attr('data-id');
        if (classD != 'undefined' && classD != undefined) {
            if (classD == 'delivery') {
                $('.savColor_' + classD + ' #secondary').attr('fill', '#FFF');
                $('.savColor_' + classD + ' #primary').attr('stroke', '#FFF');
                $('.savColor_' + classD + ' #primary-2').attr('stroke', '#FFF');
                $('.savColor_' + classD + ' #primary-3').attr('stroke', '#FFF');
                $('.savColor_' + classD + ' #primary-4').attr('stroke', '#FFF');
            } else {
                $('.savColor_' + classD).attr('fill', '#FFF');
            }
        }
    });
</script>
