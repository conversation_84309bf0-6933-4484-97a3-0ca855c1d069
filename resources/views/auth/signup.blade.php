<!DOCTYPE html>
<html lang="en">
<head>
    <title>Vendor | Sign Up - {{env('APP_NAME')}}</title>
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <meta name="keywords" content="" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
    <link href="{{asset('assets/plugins/global/plugins.bundle.css')}}" rel="stylesheet" type="text/css" />
    <link href="{{asset('assets/css/style.bundle.css')}}" rel="stylesheet" type="text/css" />
    <script>// Frame-busting to prevent site from being loaded within a frame without permission (click-jacking) if (window.top != window.self) { window.top.location.replace(window.self.location.href); }</script>
</head>
<body id="kt_body" class="app-blank bgi-size-cover bgi-attachment-fixed bgi-position-center">
    <script>
        var defaultThemeMode = "light"; var themeMode; if (document.documentElement) { if (document.documentElement.hasAttribute("data-bs-theme-mode")) { themeMode = document.documentElement.getAttribute("data-bs-theme-mode"); } else { if (localStorage.getItem("data-bs-theme") !== null) { themeMode = localStorage.getItem("data-bs-theme"); } else { themeMode = defaultThemeMode; } } if (themeMode === "system") { themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light"; } document.documentElement.setAttribute("data-bs-theme", themeMode); }
    </script>
    <div class="d-flex flex-column flex-root" id="kt_app_root">
        <style>
            body {
                background-image: url("{{asset('assets/media/auth/bg10.jpeg')}}");
            }
            [data-bs-theme="dark"] body {
                background-image: url("{{asset('assets/media/auth/bg10-dark.jpeg')}}");
            }
        </style>
        <div class="d-flex flex-column flex-lg-row flex-column-fluid">
            <div class="d-flex flex-lg-row-fluid">
                <div class="d-flex flex-column flex-center pb-0 pb-lg-10 p-10 w-100">
                    <img class="theme-light-show mx-auto mw-100 w-150px w-lg-300px mb-10 mb-lg-20"
                        src="{{asset('assets/media/auth/new_selelr_bg_2-removebg-preview.png')}}" alt="" />
                    <img class="theme-dark-show mx-auto mw-100 w-150px w-lg-300px mb-10 mb-lg-20"
                        src="{{asset('assets/media/auth/agency-dark.png')}}" alt="" />
                </div>
            </div>
            <div class="d-flex flex-column-fluid flex-lg-row-auto justify-content-center justify-content-lg-end p-12">
                <div class="bg-body d-flex flex-column flex-center rounded-4 w-md-600px p-10">
                    <div class="d-flex flex-center flex-column align-items-stretch h-lg-100 w-md-400px">
                        <div class="d-flex flex-center flex-column flex-column-fluid pb-15 pb-lg-20">
                            <form class="form w-100" id="kt_sign_in_form" action="{{ route('seller.saveSignup') }}" method="POST">
                                @csrf
                                <div class="text-center mb-11">
                                    <h1 class="text-gray-900 fw-bolder mb-3">Sign up</h1>
                                    <div class="text-gray-500 fw-semibold fs-6">Your Account</div>
                                </div>
                                @if($errors->any())
                                    <ul class="errors-list" style="color: red;">
                                        @foreach($errors->all() as $error)
                                            <li>{{$error}}</li>
                                        @endforeach
                                    </ul>
                                @endif
                                @if(session()->has('message'))
                                    <div class="alert alert-success" role="alert">
                                        {{ session()->get('message') }}
                                    </div>
                                @endif
                                <div class="fv-row mb-8">
                                    <select name="country" autocomplete="off" class="form-select form-select-solid form-select-lg bg-transparent">
                                        <option>Select Country</option>
                                        @foreach($countries as $country)
                                        <option value="{{$country->id}}" data-phonecode="{{$country->phonecode}}" @if(old('country') != '' && old('country') == $country->id) selected @endif>{{$country->nicename}}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="fv-row mb-8">
                                    <input type="text" placeholder="Name" name="name" required autocomplete="off" class="form-control bg-transparent" value="{{old('name')}}"/>
                                </div>
                                <div class="fv-row mb-8">
                                    <input type="email" placeholder="Email" autocomplete="off" name="email" class="form-control bg-transparent" value="{{old('email')}}"/>
                                </div>
                                <div class="fv-row mb-8">
                                    <div class="d-flex">
                                        <select name="phone_country_code" autocomplete="off" class="form-select form-select-solid form-select-lg bg-transparent me-2">
                                            <option>Code</option>
                                            @foreach($countries as $country)
                                            <option value="{{$country->phonecode}}" @if(old('phone_country_code') != '' && old('phone_country_code') == $country->phonecode) selected @endif>+{{$country->phonecode}} ({{$country->nicename}})</option>
                                            @endforeach
                                        </select>
                                        <input type="text" placeholder="Phone Number" autocomplete="off" name="phone_number" class="form-control bg-transparent" value="{{old('phone_number')}}"/>
                                    </div>
                                </div>
                                <div class="fv-row mb-8">
                                    <div class="d-flex">
                                        <select name="whatsapp_country_code" autocomplete="off" class="form-select form-select-solid form-select-lg bg-transparent me-2">
                                            <option value="">Code</option>
                                            @foreach($countries as $country)
                                            <option value="{{$country->phonecode}}" @if(old('whatsapp_country_code') != '' && old('whatsapp_country_code') == $country->phonecode) selected @endif>+{{$country->phonecode}} ({{$country->nicename}})</option>
                                            @endforeach
                                        </select>
                                        <input type="text" placeholder="WhatsApp Number" autocomplete="off" name="whatsapp_number" class="form-control bg-transparent" value="{{old('whatsapp_number')}}"/>
                                    </div>
                                    <div class="form-check mt-2">
                                        <input class="form-check-input" type="checkbox" id="same_as_phone" autocomplete="off">
                                        <label class="form-check-label" for="same_as_phone">
                                            Same as phone number
                                        </label>
                                    </div>
                                </div>
                                <div class="fv-row mb-3">
                                    <div class="position-relative mb-3">
                                        <input class="form-control bg-transparent" type="password" autocomplete="off" placeholder="Password" name="password" id="password" value="{{old('password')}}"/>
                                        <span class="btn btn-sm btn-icon position-absolute translate-middle top-50 end-0 me-n2" data-kt-password-meter-control="visibility">
                                            <i class="ki-outline ki-eye-slash fs-2 viewEye viewEye_password" data-id="password"></i>
                                            <i class="ki-outline ki-eye fs-2 d-none hideEye hideEye_password" data-id="password"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="fv-row mb-3">
                                    <div class="position-relative mb-3">
                                        <input class="form-control bg-transparent" type="password" placeholder="Confirm Password" name="password_confirmation" id="password_confirmation" autocomplete="off" />
                                    </div>
                                </div>
                                <div class="fv-row mb-3">
                                    <div class="form-check form-check-custom form-check-solid">
                                        <input class="form-check-input" type="checkbox" name="terms" id="kt_sign_up_agree" value="1" required/>
                                        <label class="form-check-label" for="kt_sign_up_agree">
                                            I Accept the <a href="{{ route('privacy_policy_tedallal') }}" target="_blank">Privacy Policy</a>
                                        </label>
                                    </div>
                                </div>
                                <div class="d-grid mb-10">
                                    <button type="submit" id="kt_sign_in_submit" class="btn btn-primary">
                                        <span class="indicator-label">Sign Up</span>
                                    </button>
                                </div>
                                <div class="text-gray-500 text-center fw-semibold fs-6">Already a member? 
									<a href="{{ route('seller.login') }}" class="link-primary">Log In</a></div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>var hostUrl = "{{asset('assets/')}}";</script>
    <script src="{{asset('assets/plugins/global/plugins.bundle.js')}}"></script>
    <script src="{{asset('assets/js/scripts.bundle.js')}}"></script>
    <script>
        $(document).ready(function() {
            $('select').select2();

            $("select[name='country']").change(function() {
                var selectedCountry = $(this).find('option:selected');
                var phoneCode = selectedCountry.data('phonecode');
                $("select[name='phone_country_code']").val(phoneCode).trigger('change');
            });
        });

        $("body").on('click', '.viewEye', function () {
            var idData = $(this).attr('data-id');
            $(".viewEye_" + idData).addClass('d-none');
            $(".hideEye_" + idData).removeClass('d-none');
            $('#' + idData).attr('type', 'text');
        });
        $("body").on('click', '.hideEye', function () {
            var idData = $(this).attr('data-id');
            $('.hideEye_' + idData).addClass('d-none');
            $(".viewEye_" + idData).removeClass('d-none');
            $('#' + idData).attr('type', 'password');
        });

        $("#same_as_phone").change(function() {
            if(this.checked) {
                $("input[name='whatsapp_number']").val($("input[name='phone_number']").val());
                var phoneCountryCode = $("select[name='phone_country_code']").val();
                $("select[name='whatsapp_country_code']").val(phoneCountryCode).trigger('change');
            } else {
                $("input[name='whatsapp_number']").val('');
                $("select[name='whatsapp_country_code']").val('').trigger('change');
            }
        });
    </script>
</body>
</html>