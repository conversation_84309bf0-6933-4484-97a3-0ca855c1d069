@extends('layouts.app')
@section('title', __('Login'))
@section('content')
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
		integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
		crossorigin="anonymous" referrerpolicy="no-referrer" />
	<!-- main Section -->
	<style>
		.password-toggle-icon {
			position: absolute !important;
			top: 50% !important;
			right: 10px !important;
			transform: translateY(-50%) !important;
			cursor: pointer !important;
			color: #666 !important;
			font-size: 18px !important;
		}
	</style>
	<div class="container">
		<div class="row">
			<div class="col-md-12">
				<div class="loginsignup-area">
					<div class="loginsignup text-center" style="background: #0d8f8b;">
						<div class="logo">
							<a href="{{ url('/') }}">
							</a>
						</div>
						<form id="login_form" method="POST" class="login-box" action="{{ route('backend.login.submit') }}">
							@csrf
							@if (Session::has('error'))
								<div class="alert alert-danger">
									{{ Session::get('error') }}
								</div>
							@endif
							<div class="form-group">
								<input type="email" id="email" name="email" class="form-control"
									placeholder="{{ __('Email Address') }}" value="{{ old('email') }}" required
									autocomplete="email" autofocus>
							</div>
							<div class="form-group" style="position: relative;">
								<input type="password" id="password" name="password" class="form-control"
									placeholder="{{ __('Password') }}" required autocomplete="current-password">
								<span class="password-toggle-icon" data-type="hide"><i class="fa fa-eye-slash"
										aria-hidden="true"></i></span>
							</div>
							<input type="submit" class="btn login-btn" value="{{ __('Login') }}"
								style="background: #ffffff;color: #0a4a4a;">
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- /main Section -->
@endsection
@push('scripts')
	<script>
		$('body').on("click", '.password-toggle-icon', function () {
			var V_type = $(this).attr('data-type');
			if (V_type == 'hide') {
				$(this).attr('data-type', 'view');
				$(this).html('<i class="fas fa-eye"></i>');
				$('#password').attr('type', 'text');
			} else {
				$(this).attr('data-type', 'hide');
				$(this).html('<i class="fa fa-eye-slash" aria-hidden="true"></i>');
				$('#password').attr('type', 'password');
			}
		});
	</script>
@endpush