<?php

namespace App\Repositories;

use App\Interfaces\WhatsappTemplateInterface;
use App\Models\WhatsappTemplate;

class WhatsappTemplateRepository implements WhatsappTemplateInterface
{
    protected $webHookRepository;

    public function __construct(WebHookRepository $webHookRepository)
    {
        $this->webHookRepository = $webHookRepository;
    }

    public function getAllTemplates($request)
    {
        $templates = WhatsappTemplate::all();
        return $templates;
    }

    public function fetchTemplateData($request)
    {
        $templateId = $request->template_id;
        $templateSlug = $request->slug;

        $template = WhatsappTemplate::where('id', $templateId)->first();

        if (!$template) {
            return response()->json(['error' => 'Template not found'], 404);
        }

        $fields = $template->fields->toArray();

        return response()->json(['fields' => $fields]);
    }

    public function sendWhatsappMessage($request)
    {
    }
}