<?php

namespace App\Repositories;

use App\Interfaces\CronjobInterface;
use App\Models\SettingLog;

class CronjobRepository implements CronjobInterface
{
    protected $webHookRepository;

    public function __construct(WebHookRepository $webHookRepository)
    {
        $this->webHookRepository = $webHookRepository;
    }

    public function AccessTokenReminderCronjob($request)
    {
       $settingLog = SettingLog::where('key', 'WHATSAPP_ACCESS_TOKEN')->orderBy('created_at', 'desc')->limit(1)->first();

       $sendReminder = false;
       if($settingLog){
        if(strtotime($settingLog->created_at) < strtotime('-30 days')){
            $sendReminder = true;
        }else{
            $sendReminder = false;
        }
       }else{
        $sendReminder = true;
       }

       if($sendReminder){
           $this->webHookRepository->sendWhatsAppMessage(getUser()->phone, 'Your WhatsApp access token is about to expire. Please renew it.');
       }

       return true;
    }
}
