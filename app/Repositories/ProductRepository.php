<?php

namespace App\Repositories;

use App\Interfaces\ProductInterface;
use App\Models\Product;

class ProductRepository implements ProductInterface
{
    public function fetchProducts($request)
    {
        $perPage = 10; // Number of products per page
        $search = $request->input('search', '');
        $page = $request->input('page', 1);

        $query = Product::query();

        if ($search) {
            $query->where('name', 'like', '%' . $search . '%');
        }

        $products = $query->paginate($perPage, ['*'], 'page', $page);

        return $products;
    }
}