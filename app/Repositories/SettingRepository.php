<?php

namespace App\Repositories;

use App\Interfaces\SettingInterface;
use App\Services\EnvService;
use App\Models\SettingLog;

class SettingRepository implements SettingInterface
{
    public function getSettings()
    {
        $dataList = array(
            'whatsapp_webhook_api_version' => config('services.whatsapp.version'),
            'whatsapp_phone_number_id' => config('services.whatsapp.phone_number_id'),
            'whatsapp_access_token' => config('services.whatsapp.access_token'),
        );

        return $dataList;
    }

    public function updateSettings($request)
    {
        $envService = new EnvService();

        $logArray = [];

        if($request->whatsapp_webhook_api_version != config('services.whatsapp.version')){
            $envService->setEnvValue('WHATSAPP_API_VERSION', $request->whatsapp_webhook_api_version);
            $logArray[] = [
                'key_name' => 'WHATSAPP_API_VERSION',
                'new_value' => $request->whatsapp_webhook_api_version,
                'old_value' => config('services.whatsapp.version'),
            ];
        }

        if($request->whatsapp_phone_number_id != config('services.whatsapp.phone_number_id')){
            $envService->setEnvValue('WHATSAPP_PHONE_NUMBER_ID', $request->whatsapp_phone_number_id);
            $logArray[] = [
                'key_name' => 'WHATSAPP_PHONE_NUMBER_ID',
                'new_value' => $request->whatsapp_phone_number_id,
                'old_value' => config('services.whatsapp.phone_number_id'),
            ];
        }

        if($request->whatsapp_access_token != config('services.whatsapp.access_token')){
            $envService->setEnvValue('WHATSAPP_ACCESS_TOKEN', $request->whatsapp_access_token);
            $logArray[] = [
                'key_name' => 'WHATSAPP_ACCESS_TOKEN',
                'new_value' => $request->whatsapp_access_token,
                'old_value' => config('services.whatsapp.access_token'),
            ];
        }

        if(count($logArray) > 0){
            SettingLog::insert($logArray);
        }

        return true;
    }
}
