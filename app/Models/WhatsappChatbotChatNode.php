<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WhatsappChatbotChatNode extends Model
{
    public $table = 'whatsapp_chatbot_chat_nodes';
    protected $fillable = ['chatbot_id', 'parent_node_id', 'node_type', 'message_text', 'template_id', 'is_root'];
    protected $casts = ['is_root' => 'boolean'];

    public function chatbot()
    {
        return $this->belongsTo(WhatsappChatbot::class);
    }

    public function parent()
    {
        return $this->belongsTo(WhatsappChatbotChatNode::class, 'parent_node_id');
    }

    public function children()
    {
        return $this->hasMany(WhatsappChatbotChatNode::class, 'parent_node_id');
    }

    public function responses()
    {
        return $this->hasMany(WhatsappChatbotNodeResponse::class);
    }

    public function template()
    {
        return $this->belongsTo(WhatsappChatbotInteractiveTemplate::class, 'template_id');
    }
}