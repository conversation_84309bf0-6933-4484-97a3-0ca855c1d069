<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WhatsappChatbotNodeResponse extends Model
{
    public $table = 'whatsapp_chatbot_node_responses';
    protected $fillable = ['node_id', 'response_text', 'next_node_id', 'order'];

    public function node()
    {
        return $this->belongsTo(WhatsappChatbotChatNode::class);
    }

    public function nextNode()
    {
        return $this->belongsTo(WhatsappChatbotChatNode::class, 'next_node_id');
    }
}
