<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WhatsappChatbotUserConversation extends Model
{
    public $table = 'whatsapp_chatbot_user_conversations';
    protected $fillable = ['chatbot_id', 'user_phone', 'current_node_id'];

    public function chatbot()
    {
        return $this->belongsTo(WhatsappChatbot::class);
    }

    public function currentNode()
    {
        return $this->belongsTo(WhatsappChatbotChatNode::class, 'current_node_id');
    }

    public function logs()
    {
        return $this->hasMany(WhatsappChatbotConversationLog::class);
    }
}