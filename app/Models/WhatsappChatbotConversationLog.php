<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WhatsappChatbotConversationLog extends Model
{
    public $table = 'whatsapp_chatbot_conversation_logs';
    protected $fillable = ['conversation_id', 'node_id', 'message_type', 'message_text'];

    public function conversation()
    {
        return $this->belongsTo(WhatsappChatbotUserConversation::class);
    }

    public function node()
    {
        return $this->belongsTo(WhatsappChatbotChatNode::class);
    }
}
