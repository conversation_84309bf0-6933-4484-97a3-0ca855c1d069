<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Whatsapp<PERSON>hatbot extends Model
{

    public $table = 'whatsapp_chatbots';
    protected $fillable = ['name', 'description'];

    public function nodes()
    {
        return $this->hasMany(WhatsappChatbotChatNode::class, 'chatbot_id');
    }

    public function templates()
    {
        return $this->hasMany(WhatsappChatbotInteractiveTemplate::class, 'chatbot_id');
    }

    public function conversations()
    {
        return $this->hasMany(WhatsappChatbotUserConversation::class, 'chatbot_id');
    }
}