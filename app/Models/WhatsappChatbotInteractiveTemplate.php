<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WhatsappChatbotInteractiveTemplate extends Model
{
    public $table = 'whatsapp_chatbot_interactive_templates';
    protected $fillable = ['chatbot_id', 'template_type', 'template_name', 'header_text', 'body_text', 'footer_text', 'whatsapp_template_id'];

    public function chatbot()
    {
        return $this->belongsTo(WhatsappChatbot::class, 'chatbot_id');
    }

    public function options()
    {
        return $this->hasMany(WhatsappChatbotTemplateOption::class, 'template_id');
    }

    public function nodes()
    {
        return $this->hasMany(WhatsappChatbotChatNode::class, 'template_id');
    }
}