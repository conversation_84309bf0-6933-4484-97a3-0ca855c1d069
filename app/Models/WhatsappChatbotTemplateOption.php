<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WhatsappChatbotTemplateOption extends Model
{
    public $table = 'whatsapp_chatbot_template_options';
    protected $fillable = ['template_id', 'option_type', 'option_text', 'option_value', 'order', 'next_node_id'];

    public function template()
    {
        return $this->belongsTo(WhatsappChatbotInteractiveTemplate::class);
    }

    public function nextNode()
    {
        return $this->belongsTo(WhatsappChatbotChatNode::class, 'next_node_id');
    }
}