<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Models\MediaOption;
use Gumlet\ImageResize;

class UploadController extends Controller
{
	/**
	 * Handle file upload without resizing.
	 *
	 * @param Request $request
	 * @return \Illuminate\Http\JsonResponse
	 */
	public function FileUpload(Request $request)
	{
		return $this->handleUpload($request, false);
	}

	/**
	 * Handle file upload with optional resizing.
	 *
	 * @param Request $request
	 * @return \Illuminate\Http\JsonResponse
	 */
	public function MediaUpload(Request $request)
	{
		return $this->handleUpload($request, true);
	}

	/**
	 * Core logic for handling file uploads.
	 *
	 * @param Request $request
	 * @param bool $resize
	 * @return \Illuminate\Http\JsonResponse
	 */
	protected function handleUpload(Request $request, bool $resize = false)
	{
		// Validate the request
		$request->validate([
			'FileName' => 'required|file|mimes:jpg,jpeg,png,gif|max:2048', // 2MB max
			'media_type' => $resize ? 'required|string' : 'nullable|string',
		]);

		// Define allowed file types
		$allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];

		// Set destination path
		$destinationPath = public_path('media');

		// Ensure directory exists
		if (!file_exists($destinationPath)) {
			mkdir($destinationPath, 0755, true);
		}

		$file = $request->file('FileName');
		$dateTime = now()->format('dmYHis');
		$extension = strtolower($file->getClientOriginalExtension());

		// Check if file type is allowed
		if (!in_array($extension, $allowedTypes)) {
			return response()->json([
				'msgType' => 'error',
				'msg' => __('Only JPG, JPEG, PNG, and GIF files are allowed'),
				'thumbnail' => '',
				'large_image' => '',
				'id' => '',
			]);
		}

		// Generate file names
		$fileName = $dateTime . Str::random(12) . '.' . $extension;
		$originalFileName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
		$fileSize = $file->getSize();

		// Thumbnail settings (for MediaUpload)
		$thumbFileName = $fileName;
		if ($resize) {
			$thumbnail = $this->getThumbnailDimensions($request->input('media_type'));
			$thumbFileName = $dateTime . Str::random(12) . '-' . $thumbnail['width'] . 'x' . $thumbnail['height'] . '.' . $extension;
		}

		// Delete existing file if it exists
		if (file_exists($destinationPath . '/' . $fileName)) {
			unlink($destinationPath . '/' . $fileName);
		}

		try {
			// Move the original file
			$file->move($destinationPath, $fileName);

			// Resize image if required (skip for GIF)
			if ($resize && !in_array($extension, ['gif'])) {
				$image = new ImageResize($destinationPath . '/' . $fileName);
				$image->resize($thumbnail['width'], $thumbnail['height'], true); // true to maintain aspect ratio
				$image->save($destinationPath . '/' . $thumbFileName);
			}

			// Save to database
			$media = MediaOption::create([
				'title' => $originalFileName,
				'alt_title' => $originalFileName,
				'thumbnail' => $thumbFileName,
				'large_image' => $fileName,
				'option_value' => $fileSize,
			]);

			return response()->json([
				'msgType' => 'success',
				'msg' => __('File uploaded successfully'),
				'thumbnail' => $thumbFileName,
				'large_image' => $fileName,
				'id' => $media->id,
			]);
		} catch (\Exception $e) {
			return response()->json([
				'msgType' => 'error',
				'msg' => __('Error uploading file: ') . $e->getMessage(),
				'thumbnail' => '',
				'large_image' => '',
				'id' => '',
			]);
		}
	}

	/**
	 * Get thumbnail dimensions based on media type.
	 *
	 * @param string $mediaType
	 * @return array
	 */
	protected function getThumbnailDimensions($mediaType)
	{
		$thumbnails = [
			'Thumbnail' => ['width' => 200, 'height' => 200],
			'Large' => ['width' => 800, 'height' => 600],
			// Add more media types as needed
		];

		return $thumbnails[$mediaType] ?? ['width' => 200, 'height' => 200]; // Default
	}
}
