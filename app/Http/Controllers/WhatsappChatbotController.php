<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\WhatsappChatbot;
use Illuminate\Support\Facades\Validator;

class WhatsappChatbotController extends Controller {

    public function getPageLoad()
    {
        $dataList = WhatsappChatbot::with(['nodes', 'templates'])->paginate(10);
        return view('backend.whatsapp-chatbot.chatbots.index', compact('dataList'));
    }

    public function getTableData(Request $request)
    {
        $search = $request->input('search');
        $query = WhatsappChatbot::query();
        if ($search) {
            $query->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('description', 'LIKE', "%{$search}%");
        }
        $dataList = $query->with(['nodes', 'templates'])->paginate(10);
        return view('backend.whatsapp-chatbot.chatbots.table', compact('dataList'));
    }

    public function saveOrUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'RecordId' => 'nullable|exists:whatsapp_chatbots,id',
            'name' => 'required|string|max:100',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'msgType' => 'error',
                'msg' => 'Validation failed',
                'errors' => $validator->errors()->toArray()
            ], 422);
        }

        $data = $validator->validated();
        $chatbot = $data['RecordId'] ? WhatsappChatbot::find($data['RecordId']) : new WhatsappChatbot();
        $chatbot->fill($data)->save();

        return response()->json([
            'msgType' => 'success',
            'msg' => $data['RecordId'] ? 'Chatbot updated' : 'Chatbot created'
        ]);
    }

    public function getChatbotById(Request $request)
    {
        $chatbot = WhatsappChatbot::with(['nodes', 'templates'])->find($request->id);
        if (!$chatbot) {
            return response()->json([
                'msgType' => 'error',
                'msg' => 'Chatbot not found'
            ], 404);
        }
        return response()->json($chatbot);
    }

    public function destroy($id)
    {
        $chatbot = WhatsappChatbot::find($id);
        if (!$chatbot) {
            return response()->json([
                'msgType' => 'error',
                'msg' => 'Chatbot not found'
            ], 404);
        }

        $chatbot->delete();
        return response()->json([
            'msgType' => 'success',
            'msg' => 'Chatbot deleted'
        ]);
    }
}