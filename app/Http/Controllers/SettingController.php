<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Interfaces\SettingInterface;
use Illuminate\Support\Facades\Validator;

class SettingController extends Controller
{
    protected SettingInterface $settingInterface;

    public function __construct(SettingInterface $settingInterface)
    {
        $this->settingInterface = $settingInterface;
    }

    public function getSettingsPageLoad(Request $request)
    {
        $dataList = $this->settingInterface->getSettings();

        return view('backend.settings', compact('dataList'));
    }

    public function updateSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'whatsapp_webhook_api_version' => 'required',
            'whatsapp_phone_number_id' => 'required',
            'whatsapp_access_token' => 'required',
        ]);

        $errors = $validator->errors();

        if ($errors->has('whatsapp_webhook_api_version')) {
            $res['msgType'] = 'error';
            $res['msg'] = $errors->first('whatsapp_webhook_api_version');
            return response()->json($res);
        }

        if ($errors->has('whatsapp_phone_number_id')) {
            $res['msgType'] = 'error';
            $res['msg'] = $errors->first('whatsapp_phone_number_id');
            return response()->json($res);
        }
        
        if ($errors->has('whatsapp_access_token')) {
            $res['msgType'] = 'error';
            $res['msg'] = $errors->first('whatsapp_access_token');
            return response()->json($res);
        }

        $response = $this->settingInterface->updateSettings($request);

        if ($response) {
            return response()->json(['msgType' => 'success', 'msg' => __('Data Updated Successfully')]);
        } else {
            return response()->json(['msgType' => 'error', 'msg' => __('Data update failed')]);
        }
    }
}