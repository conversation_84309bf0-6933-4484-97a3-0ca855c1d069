<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class WhatsappMessageLogController extends Controller
{
	public function getMessageLogPageLoad()
	{
		$dataList = DB::table('whatsapp_message_logs')->orderBy('whatsapp_message_logs.id', 'DESC')->paginate(20);
		
		return view('backend.message-logs', compact('dataList'));
	}

	public function getMessageLogTableData(Request $request)
	{
		$status = @$request->status;
		$category = @$request->category;
		$sort_by = @$request->sort_by;
		if ($sort_by == '') {
			$sort_by = 'DESC';
		}
		if ($request->ajax()) {
			$dataList = DB::table('whatsapp_message_logs')
				->where(function ($query) use ($status, $category) {
					if ($status != '') {
						$query->where('whatsapp_message_logs.status', $status);
					}
					if ($category != '') {
						$query->where('whatsapp_message_logs.category', $category);
					}
				})
				->orderBy('whatsapp_message_logs.id', $sort_by)
				->paginate(20);

			return view('backend.partials.message_logs_table', compact('dataList'))->render();
		}
	}

	public function getSentMessageLogPageLoad()
	{
		$dataList = DB::table('whatsapp_sent_message_logs')
			->select(
				'whatsapp_sent_message_logs.*',
				DB::raw('(SELECT timestamp FROM whatsapp_message_logs WHERE whatsapp_message_logs.message_id = whatsapp_sent_message_logs.message_id AND status = "sent" ORDER BY timestamp DESC LIMIT 1) as sent_at'),
				DB::raw('(SELECT timestamp FROM whatsapp_message_logs WHERE whatsapp_message_logs.message_id = whatsapp_sent_message_logs.message_id AND status = "delivered" ORDER BY timestamp DESC LIMIT 1) as delivered_at'),
				DB::raw('(SELECT timestamp FROM whatsapp_message_logs WHERE whatsapp_message_logs.message_id = whatsapp_sent_message_logs.message_id AND status = "read" ORDER BY timestamp DESC LIMIT 1) as read_at'),
				DB::raw('(SELECT timestamp FROM whatsapp_message_logs WHERE whatsapp_message_logs.message_id = whatsapp_sent_message_logs.message_id AND status = "failed" ORDER BY timestamp DESC LIMIT 1) as failed_at')
			)
			->leftJoin('whatsapp_message_logs', 'whatsapp_message_logs.message_id', '=', 'whatsapp_sent_message_logs.message_id')
			->orderBy('whatsapp_sent_message_logs.id', 'DESC')
			->paginate(20);

		return view('backend.sent-message-logs', compact('dataList'));
	}

	public function getSentMessageLogTableData(Request $request)
	{
		$search = @$request->search;
		$sort_by = @$request->sort_by;
		if ($sort_by == '') {
			$sort_by = 'DESC';
		}
		if ($request->ajax()) {
			$dataList = DB::table('whatsapp_sent_message_logs')
				->select(
					'whatsapp_sent_message_logs.*',
					DB::raw('(SELECT timestamp FROM whatsapp_message_logs WHERE whatsapp_message_logs.message_id = whatsapp_sent_message_logs.message_id AND status = "sent" ORDER BY timestamp DESC LIMIT 1) as sent_at'),
					DB::raw('(SELECT timestamp FROM whatsapp_message_logs WHERE whatsapp_message_logs.message_id = whatsapp_sent_message_logs.message_id AND status = "delivered" ORDER BY timestamp DESC LIMIT 1) as delivered_at'),
					DB::raw('(SELECT timestamp FROM whatsapp_message_logs WHERE whatsapp_message_logs.message_id = whatsapp_sent_message_logs.message_id AND status = "read" ORDER BY timestamp DESC LIMIT 1) as read_at'),
					DB::raw('(SELECT timestamp FROM whatsapp_message_logs WHERE whatsapp_message_logs.message_id = whatsapp_sent_message_logs.message_id AND status = "failed" ORDER BY timestamp DESC LIMIT 1) as failed_at')
				)
				->leftJoin('whatsapp_message_logs', 'whatsapp_message_logs.message_id', '=', 'whatsapp_sent_message_logs.message_id')
				->where(function ($query) use ($search) {
					if ($search != '') {
						$query->where('whatsapp_sent_message_logs.message_id', 'like', '%' . $search . '%')
							->orWhere('whatsapp_sent_message_logs.receiver', 'like', '%' . $search . '%')
							->orWhere('whatsapp_sent_message_logs.template_name', 'like', '%' . $search . '%');
					}
				})
				->orderBy('whatsapp_sent_message_logs.id', $sort_by)
				->paginate(20);

			return view('backend.partials.sent_message_logs_table', compact('dataList'))->render();
		}
	}
}