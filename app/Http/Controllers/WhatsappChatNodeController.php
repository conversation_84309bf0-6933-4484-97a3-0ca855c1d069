<?php

namespace App\Http\Controllers;

use App\Models\WhatsappChatbotInteractiveTemplate;
use Illuminate\Http\Request;
use App\Models\WhatsappChatbot;
use App\Models\WhatsappChatbotChatNode;
use Illuminate\Support\Facades\Validator;

class WhatsappChatNodeController extends Controller
{
    public function getPageLoad($chatBotId)
    {
        $nodes = WhatsappChatbotChatNode::where('chatbot_id', $chatBotId)
            ->with(['parent', 'template', 'responses'])
            ->paginate(10);

        $templates = WhatsappChatbotInteractiveTemplate::where('chatbot_id', $chatBotId)
            ->get();
        return view('backend.whatsapp-chatbot.chatnodes.index', compact('nodes', 'chatBotId', 'templates'));
    }

    public function getTableData($chatBotId, Request $request)
    {
        $nodes = WhatsappChatbotChatNode::where('chatbot_id', $chatBotId)
            ->with(['parent', 'template', 'responses'])
            ->paginate(10);
        return view('backend.whatsapp-chatbot.chatnodes.table', compact('nodes'));
    }

    public function saveOrUpdate($chatBotId, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'node_id' => 'nullable|exists:whatsapp_chatbot_chat_nodes,id',
            'parent_node_id' => 'nullable|exists:chat_nodes,node_id',
            'node_type' => 'required|in:MESSAGE,QUESTION,ACTION',
            'message_text' => 'required_if:node_type,MESSAGE|nullable|string',
            'template_id' => 'required_if:node_type,QUESTION|nullable|exists:interactive_templates,template_id',
            'is_root' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'msgType' => 'error',
                'msg' => 'Validation failed',
                'errors' => $validator->errors()->toArray()
            ], 422);
        }

        $data = $validator->validated();
        if (($data['is_root'] ?? false) && WhatsappChatbotChatNode::where('chatbot_id', $chatBotId)
            ->where('is_root', true)
            ->where('node_id', '!=', $data['node_id'] ?? 0)
            ->exists()
        ) {
            return response()->json([
                'msgType' => 'error',
                'msg' => 'Chatbot already has a root node',
                'errors' => ['is_root' => ['Only one root node allowed per chatbot']]
            ], 422);
        }

        $node = $data['node_id'] ? WhatsappChatbotChatNode::find($data['node_id']) : new WhatsappChatbotChatNode();
        if ($node->chatbot_id && $node->chatbot_id !== $chatBotId) {
            return response()->json([
                'msgType' => 'error',
                'msg' => 'Node does not belong to this chatbot'
            ], 404);
        }

        $node->fill(array_merge($data, ['chatbot_id' => $chatBotId]))->save();
        return response()->json([
            'msgType' => 'success',
            'msg' => $data['node_id'] ? 'Node updated' : 'Node created'
        ]);
    }
}