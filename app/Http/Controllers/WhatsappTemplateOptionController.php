<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\WhatsappChatbotChatNode;
use App\Models\WhatsappChatbotInteractiveTemplate;
use App\Models\WhatsappChatbotTemplateOption as TemplateOption;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class WhatsappTemplateOptionController extends Controller
{
    public function getPageLoad($templateId)
    {
        $options = TemplateOption::where('template_id', $templateId)->paginate(10);
        $template = WhatsappChatbotInteractiveTemplate::find($templateId);

        $nodes = WhatsappChatbotChatNode::where('chatbot_id', $template->chatbot_id)->get();

        return view('backend.whatsapp-chatbot.templates.options.index', compact('templateId', 'options', 'template', 'nodes'));
    }

    public function getTableData($templateId, Request $request)
    {
        $options = TemplateOption::where('template_id', $templateId)->paginate(10);

        return view('backend.whatsapp-chatbot.templates.options.table', compact('options'));
    }

    public function saveOrUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'template_id' => 'required|exists:whatsapp_chatbot_interactive_templates,id',
            'option_id' => 'nullable|exists:template_options,option_id',
            'option_type' => 'required|in:BUTTON,LIST_ITEM',
            'option_text' => 'required|string|max:255',
            'option_value' => 'nullable|string|max:255',
            'order' => 'integer|min:0',
            'next_node_id' => 'nullable|exists:chat_nodes,node_id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'msgType' => 'error',
                'msg' => 'Validation failed',
                'errors' => $validator->errors()->toArray()
            ], 422);
        }

        $data = $validator->validated();
        $existingOptions = TemplateOption::where('template_id', $data['template_id'])
            ->where('option_type', $data['option_type'])
            ->where('option_id', '!=', $data['option_id'] ?? 0)
            ->count();

        if ($data['option_type'] === 'BUTTON' && $existingOptions >= 3) {
            return response()->json([
                'msgType' => 'error',
                'msg' => 'Maximum 3 buttons allowed',
                'errors' => ['option_type' => ['Maximum 3 buttons allowed']]
            ], 422);
        }
        if ($data['option_type'] === 'LIST_ITEM' && $existingOptions >= 10) {
            return response()->json([
                'msgType' => 'error',
                'msg' => 'Maximum 10 list items allowed',
                'errors' => ['option_type' => ['Maximum 10 list items allowed']]
            ], 422);
        }

        $option = $data['option_id'] ? TemplateOption::find($data['option_id']) : new TemplateOption();
        if ($option->template_id && $option->template_id !== $data['template_id']) {
            return response()->json([
                'msgType' => 'error',
                'msg' => 'Option does not belong to this template'
            ], 404);
        }

        $option->fill(array_merge($data, ['template_id' => $data['template_id']]))->save();
        return response()->json([
            'msgType' => 'success',
            'msg' => $data['option_id'] ? 'Option updated' : 'Option created'
        ]);
    }

    public function destroy($templateId, $id)
    {
        $option = TemplateOption::find($id);
        if (!$option || $option->template_id !== $templateId) {
            return response()->json([
                'msgType' => 'error',
                'msg' => 'Option not found or does not belong to this template'
            ], 404);
        }

        $option->delete();
        return response()->json([
            'msgType' => 'success',
            'msg' => 'Option deleted'
        ]);
    }
}