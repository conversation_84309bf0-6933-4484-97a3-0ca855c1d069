<?php

namespace App\Http\Controllers\Cronjob;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Interfaces\CronjobInterface;

class CronjobController extends Controller
{
    protected CronjobInterface $cronjobInterface;

    public function __construct(CronjobInterface $cronjobInterface)
    {
        $this->cronjobInterface = $cronjobInterface;
    }

    public function AccessTokenReminderCronjob(Request $request)
    {
        $response = $this->cronjobInterface->AccessTokenReminderCronjob($request);

        return response()->json($response);
    }
}