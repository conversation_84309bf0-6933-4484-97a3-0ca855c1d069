<?php

namespace App\Http\Controllers;

use App\Models\WhatsappChatbotInteractiveTemplate as InteractiveTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class WhatsappInteractiveTemplateController extends Controller
{
    public function getPageLoad($chatBotId)
    {
        $templates = InteractiveTemplate::where('chatbot_id', $chatBotId)
            ->with('options')
            ->paginate(10);
        return view('backend.whatsapp-chatbot.templates.index', compact('chatBotId', 'templates'));
    }

    public function getTableData($chatBotId, Request $request)
    {
        $templates = InteractiveTemplate::where('chatbot_id', $chatBotId)
            ->with('options')
            ->paginate(10);
        return view('backend.whatsapp-chatbot.templates.table', compact('templates'));
    }

    public function getTemplateById(Request $request)
    {
        $template = InteractiveTemplate::find($request->template_id);
        if (!$template) {
            return response()->json([
                'msgType' => 'error',
                'msg' => 'Template not found'
            ], 404);
        }
        return response()->json([
            'msgType' => 'success',
            'msg' => 'Template found',
            'data' => $template
        ]);
    }

    public function saveOrUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'template_id' => 'nullable|exists:whatsapp_chatbot_interactive_templates,id',
            'chatbot_id' => 'required|exists:whatsapp_chatbots,id',
            'template_type' => 'required|in:BUTTON,LIST,PRODUCT,CUSTOM',
            'template_name' => 'required|string|max:100',
            'header_text' => 'nullable|string|max:255',
            'body_text' => 'required|string',
            'footer_text' => 'nullable|string|max:255',
            'whatsapp_template_id' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'msgType' => 'error',
                'msg' => 'Validation failed',
                'errors' => $validator->errors()->toArray()
            ], 422);
        }

        $data = $validator->validated();
        $template = $data['template_id'] ? InteractiveTemplate::find($data['template_id']) : new InteractiveTemplate();
        if ($template->chatbot_id && $template->chatbot_id != $request->chatbot_id) {
            return response()->json([
                'msgType' => 'error',
                'msg' => 'Template does not belong to this chatbot'
            ], 404);
        }

        $template->fill(array_merge($data, ['chatbot_id' => $request->chatbot_id]))->save();
        return response()->json([
            'msgType' => 'success',
            'msg' => $data['template_id'] ? 'Template updated' : 'Template created'
        ]);
    }

    public function destroy(Request $request)
    {
        $template = InteractiveTemplate::find($request->template_id);
        if (!$template || $template->chatbot_id !== $request->chatbot_id) {
            return response()->json([
                'msgType' => 'error',
                'msg' => 'Template not found or does not belong to this chatbot'
            ], 404);
        }

        $template->delete();
        return response()->json([
            'msgType' => 'success',
            'msg' => 'Template deleted'
        ]);
    }


    

}