<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Validator;
use App\Models\MediaOption;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class UserController extends Controller
{
    public function getUserById(Request $request)
    {
        $id = $request->id;
        $data = User::where('id', $id)->first();
        return response()->json($data);
    }

    public function getProfilePageLoad()
    {
        $mediaDatalist = MediaOption::orderBy('id', 'desc')->paginate(28);
        return view('backend.profile', compact('mediaDatalist'));
    }

    public function profileUpdate(Request $request)
    {
        $res = array();

        $id = $request->input('RecordId');
        $name = $request->input('name');
        $email = $request->input('email');
        $phone = $request->input('phone');
        $photo = $request->input('photo');

        $validator_array = array(
            'name' => $request->input('name'),
            'email' => $request->input('email'),
            'phone' => $request->input('phone'),
        );

        $rId = $id == '' ? '' : ',' . $id;
        $validator = Validator::make($validator_array, [
            'name' => 'required|max:191',
            'email' => 'required|max:191|unique:users,email' . $rId,
            'phone' => 'required|max:191|unique:users,phone' . $rId,
        ]);

        $errors = $validator->errors();

        if ($errors->has('name')) {
            $res['msgType'] = 'error';
            $res['msg'] = $errors->first('name');
            return response()->json($res);
        }

        if ($errors->has('email')) {
            $res['msgType'] = 'error';
            $res['msg'] = $errors->first('email');
            return response()->json($res);
        }

        if ($errors->has('phone')) {
            $res['msgType'] = 'error';
            $res['msg'] = $errors->first('phone');
            return response()->json($res);
        }

        $data = array(
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'photo' => $photo
        );

        $response = User::where('id', $id)->update($data);
        if ($response) {
            $res['msgType'] = 'success';
            $res['msg'] = __('Data Updated Successfully');
        } else {
            $res['msgType'] = 'error';
            $res['msg'] = __('Data update failed');
        }

        return response()->json($res);
    }

    public function changePassword(Request $request)
    {
        $res = array();
        $password = $request->input('password');

        $validator_array = array(
            'password' => $request->input('password'),
            'confirm_password' => $request->input('confirm_password')
        );

        $validator = Validator::make($validator_array, [
            'password' => 'required|min:6',
            'confirm_password' => 'required|same:password'
        ]);

        $errors = $validator->errors();

        if ($errors->has('password')) {
            $res['msgType'] = 'error';
            $res['msg'] = $errors->first('password');
            return response()->json($res);
        }

        if ($errors->has('confirm_password')) {
            $res['msgType'] = 'error';
            $res['msg'] = $errors->first('confirm_password');
            return response()->json($res);
        }

        $data = array(
            'password' => hash::make($password)
        );

        $response = User::where('id', Auth::user()->id)->update($data);
        if ($response) {
            $res['msgType'] = 'success';
            $res['msg'] = __('Password Changed Successfully');
        } else {
            $res['msgType'] = 'error';
            $res['msg'] = __('Password change failed');
        }

        return response()->json($res);
    }
}
