<?php

namespace App\Http\Controllers;

use App\Interfaces\ProductInterface;
use App\Models\Product;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    protected $productInterface;

    public function __construct(ProductInterface $productInterface)
    {
        $this->productInterface = $productInterface;
    }

    public function fetch(Request $request)
    {
        $products = $this->productInterface->fetchProducts($request);

        return response()->json([
            'items' => $products->items(),
            'current_page' => $products->currentPage(),
            'last_page' => $products->lastPage()
        ]);
    }
}
