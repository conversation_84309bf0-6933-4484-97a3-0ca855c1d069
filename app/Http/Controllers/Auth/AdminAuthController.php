<?php

namespace App\Http\Controllers\Auth;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Session;

class AdminAuthController extends Controller
{
    public function login(Request $request)
    {
        if (Auth::check()) {
            return to_route('backend.dashboard');
        }

        if ($request->isMethod('post')) {
            $request->validate([
                "email" => ['required', 'email'],
                "password" => ['required']
            ]);

            $email = $request->email;
            $password = $request->password;

            if (Auth::attempt(['email' => $email, 'password' => $password])) {
                return to_route('backend.dashboard');
            }

            Session::flash('error', 'Provided Credentials Are Not Valid');

            return back()->withInput();
        }

        return view('auth.login');
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return to_route('login');
    }
}
