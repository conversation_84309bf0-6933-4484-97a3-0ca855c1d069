<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Interfaces\WhatsappTemplateInterface;
use App\Models\MediaOption;

class WhatsappTemplateController extends Controller
{
    protected $whatsappTemplateInterface;

    public function __construct(WhatsappTemplateInterface $whatsappTemplateInterface)
    {
        $this->whatsappTemplateInterface = $whatsappTemplateInterface;
    }

    public function getTemplatesPageLoad(Request $request)
    {
        $templates = $this->whatsappTemplateInterface->getAllTemplates($request);
        $mediaDatalist = MediaOption::orderBy('id', 'desc')->paginate(28);

        return view('backend.templates', compact('templates', 'mediaDatalist'));
    }

    public function fetchTemplateData(Request $request)
    {
        return $this->whatsappTemplateInterface->fetchTemplateData($request);
    }
}
