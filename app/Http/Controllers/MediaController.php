<?php

namespace App\Http\Controllers;
 
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\MediaOption;

class MediaController extends Controller
{	
    //Media page load
    public function getMediaPageLoad(Request $request){
				
		$search = $request->search;

        if($search != ''){
            $media_datalist = MediaOption::where(function ($query) use ($search){
                $query->where('title', 'like', '%'.$search.'%')
                    ->orWhere('alt_title', 'like', '%'.$search.'%');
            })
			->orderBy('id','desc')
			->paginate(28);
			
            $media_datalist->appends(['search' => $search]);
			
        }else{
            $media_datalist = MediaOption::orderBy('id','desc')->paginate(28);
        }
		
		return view('backend.media', compact('media_datalist'));
    }
	
	//Get data for Media Pagination
	public function getMediaPaginationData(Request $request){

		$search = $request->search;
		
		if($request->ajax()){
			
			if($search != ''){
				$media_datalist = MediaOption::where(function ($query) use ($search){
					$query->where('title', 'like', '%'.$search.'%')
						->orWhere('alt_title', 'like', '%'.$search.'%');
				})
				->orderBy('id', 'desc')
				->paginate(28);
				
				$media_datalist->appends(['search' => $search]);
				
			}else{
				$media_datalist = MediaOption::orderBy('id', 'desc')->paginate(28);
			}
			
			return view('backend.partials.media_pagination_data', compact('media_datalist'))->render();
		}
	}
	
	//Get data for media by id
    public function getMediaById(Request $request){

		$id = $request->id;
		
		$data = MediaOption::where('id', $id)->first();

		return response()->json($data);
	}
	
	//Save data for media
    public function mediaUpdate(Request $request){
		$res = array();
		
		$id = $request->input('RecordId');
		$title = $request->input('title');
		$alt_title = $request->input('alternative_text');

		$data = array(
			'title' => $title,
			'alt_title' => $alt_title
		);

		$response = MediaOption::where('id', $id)->update($data);
		if($response){
			$res['msgType'] = 'success';
			$res['msg'] = __('Data Updated Successfully');
		}else{
			$res['msgType'] = 'error';
			$res['msg'] = __('Data update failed');
		}
		
		return response()->json($res);
    }
	
	//Delete data for Media
	public function onMediaDelete(Request $request){
		
		$res = array();

		$id = $request->id;
		
		$res['msgType'] = 'error';
		$res['msg'] = __('Data remove failed');
		return response()->json($res);
	}
	
	//Get data for Global Media
	public function getGlobalMediaData(Request $request){

		$search = $request->search;
		
		if($request->ajax()){
			
			if($search != ''){
				$mediaDatalist = MediaOption::where(function ($query) use ($search){
					$query->where('title', 'like', '%'.$search.'%')
						->orWhere('alt_title', 'like', '%'.$search.'%');
				})
				->orderBy('id', 'desc')
				->paginate(28);
				
				$mediaDatalist->appends(['search' => $search]);
				
			}else{
				$mediaDatalist = MediaOption::where('type','image')->orderBy('id', 'desc')->paginate(28);
			}

			return view('backend.partials.global_media_pagination_data', compact('mediaDatalist'))->render();
		}
	}
}
