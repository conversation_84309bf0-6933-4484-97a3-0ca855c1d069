<?php

namespace App\Services;

use GuzzleHttp\Client;

class WhatsAppService
{
    protected $client;
    protected $accessToken;
    protected $phoneNumberId;

    public function __construct()
    {
        $this->client = new Client(['base_uri' => 'https://graph.facebook.com/v20.0/']);
        $this->accessToken = config('services.whatsapp.access_token');
        $this->phoneNumberId = config('services.whatsapp.phone_number_id');
    }

    public function sendTextMessage($to, $text)
    {
        $response = $this->client->post("{$this->phoneNumberId}/messages", [
            'headers' => [
                'Authorization' => "Bearer {$this->accessToken}",
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'messaging_product' => 'whatsapp',
                'to' => $to,
                'type' => 'text',
                'text' => ['body' => $text],
            ],
        ]);

        return json_decode($response->getBody(), true);
    }

    public function sendInteractiveMessage($to, $template)
    {
        $buttons = $template->options->map(function ($option) {
            return [
                'type' => 'reply',
                'reply' => [
                    'id' => $option->option_value ?? $option->option_text,
                    'title' => $option->option_text,
                ],
            ];
        })->toArray();

        $response = $this->client->post("{$this->phoneNumberId}/messages", [
            'headers' => [
                'Authorization' => "Bearer {$this->accessToken}",
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'messaging_product' => 'whatsapp',
                'to' => $to,
                'type' => 'interactive',
                'interactive' => [
                    'type' => strtolower($template->template_type),
                    'header' => $template->header_text ? ['type' => 'text', 'text' => $template->header_text] : null,
                    'body' => ['text' => $template->body_text],
                    'footer' => $template->footer_text ? ['text' => $template->footer_text] : null,
                    'action' => [
                        'buttons' => $template->template_type === 'BUTTON' ? $buttons : null,
                        'sections' => $template->template_type === 'LIST' ? [['title' => 'Options', 'rows' => $buttons]] : null,
                    ],
                ],
            ],
        ]);

        return json_decode($response->getBody(), true);
    }
}