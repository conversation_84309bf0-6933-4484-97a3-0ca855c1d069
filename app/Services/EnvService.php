<?php

namespace App\Services;

use Illuminate\Support\Facades\Artisan;

class EnvService
{
    /**
     *
     * @param string $key
     * @param string $value
     * @return bool
     */
    public function setEnvValue(string $key, string $value): bool
    {
        $envFilePath = app()->environmentFilePath();
        $envContents = file_get_contents($envFilePath);

        $escapedKey = preg_quote($key, '/');
        $value = $this->formatEnvValue($value);
        if (preg_match("/^{$escapedKey}=(.*)$/m", $envContents)) {
            $envContents = preg_replace("/^{$escapedKey}=(.*)$/m", "{$key}={$value}", $envContents);
        } else {
            $envContents .= "\n{$key}={$value}\n";
        }
        $result = file_put_contents($envFilePath, $envContents) !== false;

        if ($result) {
            Artisan::call('config:clear');
        }

        return $result;
    }

    /**
     *
     * @param string $key
     * @return string|null
     */
    public function getEnvValue(string $key): ?string
    {
        $envFilePath = app()->environmentFilePath();
        $envContents = file_get_contents($envFilePath);

        $escapedKey = preg_quote($key, '/');

        if (preg_match("/^{$escapedKey}=(.*)$/m", $envContents, $matches)) {
            return trim($matches[1]);
        }

        return null;
    }

    /**
     *
     * @param string $value
     * @return string
     */
    private function formatEnvValue(string $value): string
    {
        if (str_contains($value, ' ')) {
            $value = '"' . $value . '"';
        }

        return $value;
    }
}
