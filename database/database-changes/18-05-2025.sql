-- Creating the whatsapp_message_logs table
CREATE TABLE whatsapp_message_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    message_id VARCHAR(255) NOT NULL,
    whatsapp_account_id VARCHAR(50) NOT NULL,
    phone_number_id VARCHAR(50) NOT NULL,
    display_phone_number VARCHAR(20) NOT NULL,
    recipient_id VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL,
    timestamp BIGINT NOT NULL,
    conversation_id VARCHAR(50),
    conversation_type VARCHAR(20),
    expiration_timestamp BIGINT,
    pricing_billable BOOLEAN,
    pricing_model VARCHAR(10),
    pricing_category VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_recipient_id (recipient_id),
    INDEX idx_status (status),
    INDEX idx_timestamp (timestamp),
    INDEX idx_whatsapp_account_id (whatsapp_account_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Creating the whatsapp_message_errors table
CREATE TABLE whatsapp_message_errors (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    message_log_id BIGINT NOT NULL,
    error_code INTEGER NOT NULL,
    error_title VARCHAR(255) NOT NULL,
    error_message TEXT NOT NULL,
    error_details TEXT,
    error_href VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (message_log_id) REFERENCES whatsapp_message_logs(id) ON DELETE CASCADE,
    INDEX idx_message_log_id (message_log_id),
    INDEX idx_error_code (error_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Creating the whatsapp_message_contents table
CREATE TABLE whatsapp_message_contents (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    message_log_id BIGINT NOT NULL,
    body_text TEXT,
    message_type VARCHAR(20),
    direction ENUM('incoming', 'outgoing') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (message_log_id) REFERENCES whatsapp_message_logs(id) ON DELETE CASCADE,
    INDEX idx_message_log_id (message_log_id),
    INDEX idx_direction (direction)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;




-- Creating the whatsapp_templates table
CREATE TABLE whatsapp_templates (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    language VARCHAR(10) NOT NULL,
    template_type VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_slug (slug),
    INDEX idx_template_type (template_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Creating the whatsapp_template_fields table
CREATE TABLE whatsapp_template_fields (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    whatsapp_template_id BIGINT UNSIGNED NOT NULL,
    field_key VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (whatsapp_template_id) REFERENCES whatsapp_templates(id) ON DELETE CASCADE,
    INDEX idx_whatsapp_template_id (whatsapp_template_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;