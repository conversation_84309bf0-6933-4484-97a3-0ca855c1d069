function onPaginationDataLoad(page) {
	var search = 	$("#search").val();
	var status =	$("#status").val();
	$.ajax({
		url:base_url + "/backend/getCustomerRewardTableData/"+u_id+"?page="+page+"&search="+search+"&status="+status,
		success:function(data){
			$('#tp_datalist').html(data);
		}
	});
}
$(document).on('click', '.users_pagination nav  a', function(event){
    event.preventDefault(); 
    var page = $(this).attr('href').split('page=')[1];
    onPaginationDataLoad(page);
});