//Start create google map
var map; // Declare the map variable
// Function to initialize the map
function initializeMap(map_div_id, temp_latitude, temp_longitude) {
    var current_latitude = parseFloat(temp_latitude);
    var current_longitude = parseFloat(temp_longitude);
    const initialLocation = {
        lat: current_latitude,
        lng: current_longitude,
    }; // Default location (San Francisco) lat: 25.13018633759462, lng: 51.164584375000004
    const map = new google.maps.Map(document.getElementById(map_div_id), {
        center: initialLocation,
        zoom: 18,
    });
    // Create a draggable marker
    const marker = new google.maps.Marker({
        position: initialLocation,
        map: map,
        icon: "https://technologylab.qa/uploads/icon/map.png",
        draggable: true,
    });
    $(".new_gllpLatitude").val(current_latitude);
    $(".new_gllpLongitude").val(current_longitude);
    // Add event listener for marker dragend
    new google.maps.event.addListener(marker, "dragend", function (event) {
        const newPosition = {
            lat: event.latLng.lat(),
            lng: event.latLng.lng(),
        };
        $(".new_gllpLatitude").val(event.latLng.lat());
        $(".new_gllpLongitude").val(event.latLng.lng());
        console.log("New marker position:", newPosition);
    });
    // Initialize Autocomplete
    if (map_div_id == "editAuctionMap" || map_div_id == "editMap") {
        var input = document.getElementById("edit-location-text-box");
    } else {
        var input = document.getElementById("location-text-box");
    }
    var autocomplete = new google.maps.places.Autocomplete(input);
    // Add event listener for place changes
    autocomplete.addListener("place_changed", function (event) {
        var place = autocomplete.getPlace();
        if (!place.geometry) {
            // Place details not available for this place.
            alert("Place details not available for this location.");
            return;
        }
        // Center the map on the selected place
        map.setCenter(place.geometry.location);
        $(".new_gllpLatitude").val(place.geometry.location.lat());
        $(".new_gllpLongitude").val(place.geometry.location.lng());
        // reset the previous marker (if any)
        marker.setPosition(place.geometry.location);
    });
}
