var $ = jQuery.noConflict();
var RecordId = "";
var BulkAction = "";
var ids = [];

$(function () {
  "use strict";

  $.ajaxSetup({
    headers: {
      "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
    },
  });

  resetForm("DataEntry_formId");

  $("#submit-form").on("click", function () {
    $("#DataEntry_formId").submit();
  });

  $(document).on(
    "click",
    ".tp_pagination nav ul.pagination a",
    function (event) {
      event.preventDefault();
      var page = $(this).attr("href").split("page=")[1];
      onPaginationDataLoad(page);
    }
  );

  $("input:checkbox").prop("checked", false);

  $(".checkAll").on("click", function () {
    $("input:checkbox").not(this).prop("checked", this.checked);
  });

  $("#is_featured").chosen();
  $("#is_featured").trigger("chosen:updated");

  //$("#lan").chosen();
  //$("#lan").trigger("chosen:updated");

  $("#is_publish").chosen();
  $("#is_publish").trigger("chosen:updated");

  $("#media_select_file").on("click", function () {
    var thumbnail = $("#thumbnail").val();
    if (thumbnail != "") {
      $("#brand_thumbnail").val(thumbnail);
      $("#view_thumbnail_image").html(
        '<img src="' + public_path + "/media/" + thumbnail + '">'
      );
    }

    $("#remove_thumbnail").show();
    $("#global_media_modal_view").modal("hide");
  });

  //$("#language_code").val(0).trigger("chosen:updated");

  //$("#language_code").on("change", function () {
  //onRefreshData();
  //});
});

function onCheckAll() {
  $(".checkAll").on("click", function () {
    $("input:checkbox").not(this).prop("checked", this.checked);
  });
}

function onPaginationDataLoad(page) {
  $.ajax({
    url:
      base_url +
      "/backend/getAdsRequestTableData?page=" +
      page +
      "&search=" +
      $("#search").val() +
      "&adsModule=" +
      $("#adsModule").val(),
    //+"&language_code="+$('#language_code').val(),
    success: function (data) {
      $("#tp_datalist").html(data);
      onCheckAll();
    },
  });
}

function onRefreshData() {
  var page = $(".pagination .active .page-link").html();
  if (page == "" || page == 0) {
    page = 1;
  }
  var adsModule = $("#adsModule").val();
  $.ajax({
    url:
      base_url +
      "/backend/getAdsRequestTableData?search=" +
      $("#search").val() +
      "&page=" +
      page +
      "&adsModule=" +
      adsModule,
    //+"&language_code="+$('#language_code').val(),
    success: function (data) {
      $("#tp_datalist").html(data);
      onCheckAll();
    },
  });
}

function onSearch() {
  $.ajax({
    url:
      base_url +
      "/backend/getAdsRequestTableData?search=" +
      $("#search").val() +
      "&adsModule=" +
      $("#adsModule").val(),
    success: function (data) {
      $("#tp_datalist").html(data);
      onCheckAll();
    },
  });
}

function resetForm(id) {
  $("#" + id).each(function () {
    this.reset();
  });

  $("#is_featured").trigger("chosen:updated");
  //$("#lan").trigger("chosen:updated");
  $("#is_publish").trigger("chosen:updated");
}

function onListPanel() {
  $(".parsley-error-list").hide();
  $("#list-panel, .btn-form").show();
  $("#form-panel, .btn-list").hide();
}

function onConfirmReject() {
  $.ajax({
    type: "POST",
    url: base_url + "/backend/rejectAdsRequest",
    data: "id=" + RecordId,
    success: function (response) {
      var msgType = response.msgType;
      var msg = response.msg;

      if (msgType == "success") {
        onSuccessMsg(msg);
        onRefreshData();
        onListPanel();
      } else {
        onErrorMsg(msg);
      }

      onCheckAll();
    },
  });
}

function onReject() {
  RecordId = $("#RecordId").val();
  var msg = TEXT["Do you really want to reject this record"];
  onCustomModal(msg, "onConfirmReject");
}

function onFormPanel() {
  resetForm("DataEntry_formId");
  RecordId = "";

  $("#remove_thumbnail").hide();
  $("#brand_thumbnail").html("");

  $("#is_featured").trigger("chosen:updated");
  //$("#lan").trigger("chosen:updated");
  $("#is_publish").trigger("chosen:updated");

  $("#list-panel, .btn-form").hide();
  $("#form-panel, .btn-list").show();
}

function onEditPanel() {
  $("#list-panel, .btn-form").hide();
  $("#form-panel, .btn-list").show();
}

function onMediaImageRemove(type) {
  $("#brand_thumbnail").val("");
  $("#remove_thumbnail").hide();
}

function showPerslyError() {
  $(".parsley-error-list").show();
}

jQuery("#DataEntry_formId").parsley({
  listeners: {
    onFieldValidate: function (elem) {
      if (!$(elem).is(":visible")) {
        return true;
      } else {
        showPerslyError();
        return false;
      }
    },
    onFormSubmit: function (isFormValid, event) {
      if (isFormValid) {
        onConfirmWhenAddEdit();
        return false;
      }
    },
  },
});

function onConfirmWhenAddEdit() {
  $.ajax({
    type: "POST",
    url: base_url + "/backend/saveAdsRequestData",
    data: $("#DataEntry_formId").serialize(),
    success: function (response) {
      var msgType = response.msgType;
      var msg = response.msg;

      if (msgType == "success") {
        resetForm("DataEntry_formId");
        onRefreshData();
        onSuccessMsg(msg);
        onListPanel();
      } else {
        onErrorMsg(msg);
      }

      onCheckAll();
    },
  });
}

function onEdit(id) {
  RecordId = id;
  var msg = TEXT["Do you really want to view this record"];
  onCustomModal(msg, "onLoadEditData");
}

function onLoadEditData() {
  $.ajax({
    type: "POST",
    url: base_url + "/backend/getAdsRequestById",
    data: "id=" + RecordId,
    success: function (response) {
      var data = response;
      $("#RecordId").val(data.id);
      $("#page_type").val(data.page_type).trigger("chosen:updated");
      $("#user_id").val(data.user_id).trigger("chosen:updated");
      $("#title").val(data.title);
      $("#title_ar").val(data.title_ar);
      $("#description").val(data.description);
      $("#description_ar").val(data.description_ar);
      $("#product_id").val(data.product_id).trigger("chosen:updated");
      $("#shop_id").val(data.shop_id).trigger("chosen:updated");
      $("#url").val(data.url);
      $("#start_date").val(data.start_date);
      $("#end_date").val(data.end_date);
      $("#reorder").val(data.reorder);
      $("#is_publish").val(data.is_publish).trigger("chosen:updated");
      $("#comment").val(data.comment);

      let selectedOption = $(".adsModuletype").find(":selected");
      var deatils = selectedOption.attr("data-details");
      $(".recommendedImageDetails").html(deatils);

      if (data.thumbnail != null) {
        $("#brand_thumbnail").val(data.thumbnail);
        $("#view_thumbnail_image").html(
          '<img src="' + public_path + "/media/" + data.thumbnail + '">'
        );
        $("#remove_thumbnail").show();
      } else {
        $("#brand_thumbnail").val("");
        $("#view_thumbnail_image").html("");
        $("#remove_thumbnail").hide();
      }

      if (data.is_publish == 1) {
        $("form input, form select, textarea")
          .prop("disabled", true)
          .trigger("chosen:updated");

        $("#submit-form ").hide();
      }

      onEditPanel();
    },
  });
}

function onDelete(id) {
  RecordId = id;
  var msg = TEXT["Do you really want to delete this record"];
  onCustomModal(msg, "onConfirmDelete");
}

function onConfirmDelete() {
  $.ajax({
    type: "POST",
    url: base_url + "/backend/deleteAdsRequest",
    data: "id=" + RecordId,
    success: function (response) {
      var msgType = response.msgType;
      var msg = response.msg;

      if (msgType == "success") {
        onSuccessMsg(msg);
        onRefreshData();
      } else {
        onErrorMsg(msg);
      }

      onCheckAll();
    },
  });
}

$("body").on("change", ".adsModuletype", function () {
  var id = $(this).val();
  if (id != "") {
    let selectedOption = $(this).find(":selected");
    var deatils = selectedOption.attr("data-details");
    $(".recommendedImageDetails").html(deatils);
  }
});
