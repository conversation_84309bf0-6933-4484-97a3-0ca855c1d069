var $ = jQuery.noConflict();
var RecordId = "";
var TemplateId = $("#template_id").val() || 0;

$(function () {
    "use strict";

    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    resetForm("DataEntry_formId");

    $("#submit-form").on("click", function () {
        $("#DataEntry_formId").submit();
    });

    $(document).on("click", "ul.pagination a", function (event) {
        event.preventDefault();
        var page = $(this).attr("href").split("page=")[1];
        onPaginationDataLoad(page);
    });
});

function onPaginationDataLoad(page) {
    $.ajax({
        url:
            base_url +
            "/backend/whatsapp-chatbot/templates/options/get-table-data/"+ TemplateId + "?page=" +
            page +
            "&search=" +
            $("#search").val(),
        success: function (data) {
            $("#tp_datalist").html(data);
        },
    });
}

function onRefreshData() {
    var page = $(".pagination .active .page-link").html() || 1;
    $.ajax({
        url:
            base_url +
            "/backend/whatsapp-chatbot/templates/options/get-table-data/"+ TemplateId + "?search=" +
            $("#search").val() +
            "&page=" +
            page,
        success: function (data) {
            $("#tp_datalist").html(data);
        },
    });
}

function onSearch() {
    $.ajax({
        url:
            base_url +
            "/backend/whatsapp-chatbot/templates/options/get-table-data/"+ TemplateId + "?search=" +
            $("#search").val(),
        success: function (data) {
            $("#tp_datalist").html(data);
        },
    });
}

function resetForm(id) {
    $("#" + id).each(function () {
        this.reset();
    });
    $("#option_type").trigger("chosen:updated");
    $("#next_node_id").trigger("chosen:updated");
}

function onListPanel() {
    $(".parsley-error-list").hide();
    $("#list-panel, .btn-form").show();
    $("#form-panel, .btn-list").hide();
}

function onFormPanel() {
    resetForm("DataEntry_formId");
    RecordId = "";

    $("#list-panel, .btn-form").hide();
    $("#form-panel, .btn-list").show();

    

}

function onEditPanel() {
    $("#list-panel, .btn-form").hide();
    $("#form-panel, .btn-list").show();
}

function showParsleyError() {
    $(".parsley-error-list").show();
}

jQuery("#DataEntry_formId").parsley({
    listeners: {
        onFieldValidate: function (elem) {
            if (!$(elem).is(":visible")) {
                return true;
            } else {
                showParsleyError();
                return false;
            }
        },
        onFormSubmit: function (isFormValid, event) {
            if (isFormValid) {
                onConfirmWhenAddEdit();
                return false;
            }
        },
    },
});

function onConfirmWhenAddEdit() {
    $.ajax({
        type: "POST",
        url: base_url + "/backend/whatsapp-chatbot/templates/options/save-or-update",
        data: $("#DataEntry_formId").serialize(),
        success: function (response) {
            var msgType = response.msgType;
            var msg = response.msg;

            if (msgType === "success") {
                resetForm("DataEntry_formId");
                onRefreshData();
                onSuccessMsg(msg);
                onListPanel();
            } else {
                onErrorMsg(msg);
            }
        },
        error: function (xhr) {
            var errors = xhr.responseJSON.errors || {};
            var errorMsg = xhr.responseJSON.msg || "An error occurred";
            onErrorMsg(errorMsg + ": " + Object.values(errors).flat().join(", "));
        },
    });
}

function onEdit(id) {
    RecordId = id;
    var msg = TEXT["Do you really want to edit this record"];
    onCustomModal(msg, "onLoadEditData");
}

function onLoadEditData() {
    $.ajax({
        type: "POST",
        url: base_url + "/backend/whatsapp-chatbot/templates/options/get-option-by-id",
        data: "option_id=" + RecordId,
        success: function (response) {
            var data = response;
            $("#option_id").val(data.id);
            $("#option_type").val(data.option_type).trigger("chosen:updated");
            $("#option_text").val(data.option_text);
            $("#option_value").val(data.option_value || "");
            $("#order").val(data.order || "");
            $("#next_node_id").val(data.next_node_id || "").trigger("chosen:updated");
            onEditPanel();
        },
    });
}

// function onDelete(id) {
//     RecordId = id;
//     var msg = TEXT["Do you really want to delete this record"];
//     onCustomModal(msg, "onConfirmDelete");
// }

// function onConfirmDelete() {
//     $.ajax({
//         type: "POST",
//         url: base_url + "/admin/template-options/" + TemplateId + "/delete/" + RecordId,
//         data: "option_id=" + RecordId,
//         success: function (response) {
//             var msgType = response.msgType;
//             var msg = response.msg;

//             if (msgType === "success") {
//                 onSuccessMsg(msg);
//                 onRefreshData();
//             } else {
//                 onErrorMsg(msg);
//             }
//         },
//     });
// }