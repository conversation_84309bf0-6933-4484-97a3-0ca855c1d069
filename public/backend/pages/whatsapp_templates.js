
$(document).ready(function () {
    // Initialize Select2 for product_id
    $("#product_id").select2({
        placeholder: "Select Product",
        allowClear: true,
        ajax: {
            url: base_url + "/backend/products/fetch",
            dataType: "json",
            delay: 250,
            data: function (params) {
                return {
                    search: params.term || "", // Search term
                    page: params.page || 1, // Pagination
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;
                return {
                    results: data.items.map(function (item) {
                        return {
                            id: item.id,
                            text:
                                item.name +
                                (item.language
                                    ? " (" + item.language + ")"
                                    : ""),
                            slug: item.slug,
                        };
                    }),
                    pagination: {
                        more: data.current_page < data.last_page,
                    },
                };
            },
            cache: true,
        },
        minimumInputLength: 0, // Allow fetching without minimum input
        templateResult: formatProduct,
        templateSelection: formatProductSelection,
    });

    // Format the product in the dropdown
    function formatProduct(product) {
        if (!product.id) {
            return product.text;
        }
        var $product = $("<span>" + product.text + "</span>");
        return $product;
    }

    // Format the selected product
    function formatProductSelection(product) {
        return product.text || product.placeholder;
    }
});


function fetchTemplateData(selectObject) {
    var templateId = selectObject.value;
    var templateSlug =
        selectObject.options[selectObject.selectedIndex].dataset.slug;

    if (!templateId) {
        $("#dynamic-fields").empty();
        return;
    }

    $.ajax({
        url: base_url + "/backend/templates/fetch",
        method: "GET",
        data: { template_id: templateId, slug: templateSlug },
        success: function (response) {
            $("#dynamic-fields").empty();

            if (response.fields && response.fields.length > 0) {
                response.fields.forEach(function (field) {
                    var fieldHtml = `
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="${
                                    field.field_key
                                }">${field.masked_key.toUpperCase()}<span class="red">*</span></label>
                                <input type="text" name="${
                                    field.field_key
                                }" id="${field.field_key}"
                                    class="form-control" required data-parsley-required="true" 
                                    data-parsley-trigger="change">
                            </div>
                        </div>`;
                    $("#dynamic-fields").append(fieldHtml);
                });

                // Reinitialize Parsley for the form to include new fields
                $("#DataEntry_formId").parsley().destroy(); // Destroy existing Parsley instance
                $("#DataEntry_formId").parsley({
                    listeners: {
                        onFieldValidate: function (elem) {
                            if (!$(elem).is(":visible")) {
                                return true; // Skip validation for hidden fields
                            } else {
                                showPerslyError();
                                return false; // Trigger validation
                            }
                        },
                        onFormSubmit: function (isFormValid, event) {
                            event.preventDefault(); // Prevent native form submission
                            if (isFormValid) {
                                onConfirmWhenAddEdit();
                            }
                        },
                    },
                });
            }
        },
        error: function (xhr) {
            alert("Error fetching template data: " + xhr.responseJSON.message);
        },
    });
}

function showPerslyError() {
    $('.parsley-error-list').show();
}

// jQuery('#DataEntry_formId').parsley({
//     listeners: {
//         onFieldValidate: function (elem) {
//             if (!$(elem).is(':visible')) {
//                 return true;
//             } else {
//                 showPerslyError();
//                 return false;
//             }
//         },
//         onFormSubmit: function (isFormValid, event) {
//             if (isFormValid) {
//                 onConfirmWhenAddEdit();
//                 return false;
//             }
//         }
//     }
// });


 if (!$("#DataEntry_formId").hasClass("parsley-validated")) {

     $("#DataEntry_formId").parsley({
         listeners: {
             onFieldValidate: function (elem) {
                 if (!$(elem).is(":visible")) {
                     return true;
                 } else {
                     showPerslyError();
                     return false;
                 }
             },
             onFormSubmit: function (isFormValid, event) {
                 event.preventDefault(); // Prevent native form submission
                 if (isFormValid) {
                     onConfirmWhenAddEdit();
                 }
             },
         },
     });
 }

function onConfirmWhenAddEdit() {
     var $submitButton = $("#send-message");
     $submitButton.prop("disabled", true);
    $.ajax({
        type: "POST",
        url: base_url + "/backend/send-whatsapp-custom-template",
        data: $("#DataEntry_formId").serialize(),
        success: function (response) {
            var msgType = response.msgType;
            var msg = response.msg;

            if (msgType == "success") {
                $("#DataEntry_formId")[0].reset();
                onSuccessMsg(msg);
            } else {
                onErrorMsg(msg);
            }
        },
        complete: function () {
            $submitButton.prop("disabled", false); // Re-enable button
        },
    });
}

function user_Active(selectObject) {
    var userType = selectObject.value;
    if (userType == "2") {
        $(".user_list").show();
    } else {
        $(".user_list").hide();
    }
}

function onMediaImageRemove(type) {
    $("#brand_thumbnail").val("");
    $("#remove_thumbnail").hide();
}

$("#media_select_file").on("click", function () {
    var large_image = $("#large_image").val();
    if (large_image != "") {
        $("#slider_image").val(large_image);
        $("#view_slider_image").html(
            '<img src="' + public_path + "/media/" + large_image + '">'
        );
    }

    $("#remove_thumbnail").show();
    $("#global_media_modal_view").modal("hide");
});
