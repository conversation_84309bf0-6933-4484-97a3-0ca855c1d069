var $ = jQuery.noConflict();
var RecordId = '';

$(function () {
	"use strict";

	$.ajaxSetup({
		headers: {
			'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
		}
	});

	$(document).on('click', '.tp_pagination_modal nav ul.pagination a', function(event){
		event.preventDefault(); 
		var page = $(this).attr('href').split('page=')[1];
		onPaginationModalDataLoad(page);
	});
	
	$(document).on('click', '.tp_pagination nav ul.pagination a', function(event){
		event.preventDefault(); 
		var page = $(this).attr('href').split('page=')[1];
		onPaginationDataLoad(page);
	});
	
});

function onPaginationModalDataLoad(page) {alert("s");
	$.ajax({
		url:base_url + "/backend/getProductListForDealsTableData/"+deal_id+"?page="+page,
		success:function(data){
			$('#tp_datalist_modal').html(data);
		}
	});
}

function onSearchModal() {
	var search = $("#search_modal").val();
	$.ajax({
		url: base_url + "/backend/getProductListForDealsTableData/"+deal_id+"?search="+search,
		success:function(data){
			$('#tp_datalist_modal').html(data);
		}
	});
}

function onPaginationDataLoad(page) {
	$.ajax({
		url:base_url + "/backend/getDealsProductTableData/"+deal_id+"?page="+page,
		success:function(data){
			$('#tp_datalist').html(data);
		}
	});
}

function onSearch() {
	var search = $("#search").val();
	$.ajax({
		url: base_url + "/backend/getDealsProductTableData/"+deal_id+"?search="+search,
		success:function(data){
			$('#tp_datalist').html(data);
		}
	});
}

function onRefreshData() {
	$.ajax({
		url:base_url + "/backend/getDealsProductTableData/"+deal_id,
		success:function(data){
			$('#tp_datalist').html(data);
		}
	});
}

function onAddRelatedProductsModalView() {
	$('#global_media_modal_view').modal('show');
}

function onRelatedProduct(related_item_id) {
    $.ajax({
		type : 'POST',
		url: base_url + '/backend/saveDealsProductsData/'+deal_id,
		data: 'related_item_id='+related_item_id,
		success: function (response) {			
			var msgType = response.msgType;
			var msg = response.msg;
			if (msgType == "success") {
				onSuccessMsg(msg);
				onRefreshData();
			} else {
				onErrorMsg(msg);
			}
		}
	});
}

function onDelete(id) {
	RecordId = id;
	var msg = TEXT["Do you really want to delete this record"];
	onCustomModal(msg, "onConfirmDelete");	
}

function onConfirmDelete() {

    $.ajax({
		type : 'POST',
		url: base_url + '/backend/deleteDealsProduct',
		data: 'id='+RecordId,
		success: function (response) {
			var msgType = response.msgType;
			var msg = response.msg;

			if(msgType == "success"){
				onSuccessMsg(msg);
				onRefreshData();
			}else{
				onErrorMsg(msg);
			}
		}
    });
}