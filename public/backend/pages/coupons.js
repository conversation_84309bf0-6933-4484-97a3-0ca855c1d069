var $ = jQuery.noConflict();
var RecordId = '';
var BulkAction = '';
var ids = [];

$(function () {
	"use strict";

	$.ajaxSetup({
		headers: {
			'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
		}
	});

	resetForm("DataEntry_formId");
	
	$("#submit-form").on("click", function () {
        $("#DataEntry_formId").submit();
    });

	$(document).on('click', '.pagination a', function(event){
		event.preventDefault(); 
		var page = $(this).attr('href').split('page=')[1];
		onPaginationDataLoad(page);
	});
	
	$('input:checkbox').prop('checked',false);
	
    $(".checkAll").on("click", function () {
        $("input:checkbox").not(this).prop("checked", this.checked);
    });

	$("#is_publish").chosen();
	$("#is_publish").trigger("chosen:updated");
	
	$("#expire_date").datetimepicker({
		format: 'yyyy-mm-dd',
		autoclose: true,
		todayBtn: true,
		minView: 2
	});
	
});

function onCheckAll() {
    $(".checkAll").on("click", function () {
        $("input:checkbox").not(this).prop("checked", this.checked);
    });
}

function onPaginationDataLoad(page) {
	$.ajax({
		url:base_url + "/backend/getCouponsTableData?page="+page,
		success:function(data){
			$('#tp_datalist').html(data);
			onCheckAll();
		}
	});
}

function onRefreshData() {
	$.ajax({
		url:base_url + "/backend/getCouponsTableData",
		success:function(data){
			$('#tp_datalist').html(data);
			onCheckAll();
		}
	});
}

function onSearch() {
	var search = $("#search").val();
	$.ajax({
		url: base_url + "/backend/getCouponsTableData?search="+search,
		success:function(data){
			$('#tp_datalist').html(data);
			onCheckAll();
		}
	});
}

function resetForm(id) {
    $('#' + id).each(function () {
        this.reset();
    });
	
	$("#is_publish").trigger("chosen:updated");
}

function onListPanel() {
	$('.parsley-error-list').hide();
    $('#list-panel, .btn-form').show();
    $('#form-panel, .btn-list').hide();
}

function onFormPanel() {
    resetForm("DataEntry_formId");
	RecordId = '';
	
	$("#is_publish").trigger("chosen:updated");
	
    $('#list-panel, .btn-form').hide();
    $('#form-panel, .btn-list').show();
}

function onEditPanel() {
    $('#list-panel, .btn-form').hide();
    $('#form-panel, .btn-list').show();	
}

function showPerslyError() {
    $('.parsley-error-list').show();
}

jQuery('#DataEntry_formId').parsley({
    listeners: {
        onFieldValidate: function (elem) {
            if (!$(elem).is(':visible')) {
                return true;
            }
            else {
                showPerslyError();
                return false;
            }
        },
        onFormSubmit: function (isFormValid, event) {
            if (isFormValid) {
                onConfirmWhenAddEdit();
                return false;
            }
        }
    }
});

function onConfirmWhenAddEdit() {

    $.ajax({
		type : 'POST',
		url: base_url + '/backend/saveCouponsData',
		data: $('#DataEntry_formId').serialize(),
		success: function (response) {			
			var msgType = response.msgType;
			var msg = response.msg;

			if (msgType == "success") {
				resetForm("DataEntry_formId");
				onRefreshData();
				onSuccessMsg(msg);
				onListPanel();
			} else {
				onErrorMsg(msg);
			}
			
			onCheckAll();
		}
	});
}

function onEdit(id) {
	RecordId = id;
	var msg = TEXT["Do you really want to edit this record"];
	onCustomModal(msg, "onLoadEditData");	
}

function onLoadEditData() {

    $.ajax({
		type : 'POST',
		url: base_url + '/backend/getCouponsById',
		data: 'id='+RecordId,
		success: function (response) {
			var data = response.data;
			var price_range = response.price_range;
			$("#RecordId").val(data.id);
			$("#country_id").val(data.country_id).trigger("chosen:updated");
			$("#type").val(data.type).trigger("chosen:updated");
			$("#user_id").val(data.user_id).trigger("chosen:updated");
			$("#code").val(data.code);
			$("#expire_date").val(data.expire_date);
			$("#fixed").val(data.fixed).trigger("chosen:updated");
			if(data.fixed=='1'){
				
				$("#type option[value=1]").hide().trigger("chosen:updated");
				$("#type option[value=2]").hide().trigger("chosen:updated");
				$("#type option[value=3]").hide().trigger("chosen:updated");
				$("#type option[value=5]").hide().trigger("chosen:updated");
				$("#type option[value=6]").hide().trigger("chosen:updated");
				$("#type").val('4').trigger("chosen:updated");

				$(".fixed_percentage_type").html('');				
				$(".Flexible_percentage_type").html('');
				$(".fixed_percentage_type").hide();
				$(".Flexible_percentage_type").html($('.Flexible_percentage_type_div').html());
				$(".Flexible_percentage_type").show();
				
				$(".Flexible_percentage_type .start_price").val(price_range.from);
				$(".Flexible_percentage_type .end_price").val(price_range.to);
				$(".Flexible_percentage_type .percentage").val(price_range.percentage);
			}else{
				$("#type option[value=1]").show().trigger("chosen:updated");
				$("#type option[value=2]").show().trigger("chosen:updated");
				$("#type option[value=3]").show().trigger("chosen:updated");
				$("#type option[value=5]").show().trigger("chosen:updated");
				$("#type option[value=6]").show().trigger("chosen:updated");
				
				$(".Flexible_percentage_type").html('');				
				$(".fixed_percentage_type").html('');
				$(".Flexible_percentage_type").hide();
				$(".fixed_percentage_type").html($('.fixed_percentage_type_div').html());
				$(".fixed_percentage_type").show();
				$(".fixed_percentage_type .percentage").val(data.percentage);
			}
			
			$("#is_publish").val(data.is_publish).trigger("chosen:updated");
			onEditPanel();
		}
    });
}

function onDelete(id) {
	RecordId = id;
	var msg = TEXT["Do you really want to delete this record"];
	onCustomModal(msg, "onConfirmDelete");	
}

function onConfirmDelete() {

    $.ajax({
		type : 'POST',
		url: base_url + '/backend/deleteCoupons',
		data: 'id='+RecordId,
		success: function (response) {
			var msgType = response.msgType;
			var msg = response.msg;

			if(msgType == "success"){
				onSuccessMsg(msg);
				onRefreshData();
			}else{
				onErrorMsg(msg);
			}
			
			onCheckAll();
		}
    });
}

function onBulkAction() {
	ids = [];
	$('.selected_item:checked').each(function(){
		ids.push($(this).val());
	});

	if(ids.length == 0){
		var msg = TEXT["Please select record"];
		onErrorMsg(msg);
		return;
	}
	
	BulkAction = $("#bulk-action").val();
	if(BulkAction == ''){
		var msg = TEXT["Please select action"];
		onErrorMsg(msg);
		return;
	}
	
	if(BulkAction == 'publish'){
		var msg = TEXT["Do you really want to publish this records"];
	}else if(BulkAction == 'draft'){
		var msg = TEXT["Do you really want to draft this records"];
	}else if(BulkAction == 'delete'){
		var msg = TEXT["Do you really want to delete this records"];
	}
	
	onCustomModal(msg, "onConfirmBulkAction");	
}

function onConfirmBulkAction() {

    $.ajax({
		type : 'POST',
		url: base_url + '/backend/bulkActionCoupons',
		data: 'ids='+ids+'&BulkAction='+BulkAction,
		success: function (response) {
			var msgType = response.msgType;
			var msg = response.msg;

			if(msgType == "success"){
				onSuccessMsg(msg);
				onRefreshData();
				ids = [];
			}else{
				onErrorMsg(msg);
			}
			
			onCheckAll();
		}
    });
}

function checkPercentageType(selectObject){
	var percentageType = selectObject.value;
	if(percentageType=='1'){
		$("#type option[value=1]").hide().trigger("chosen:updated");
		$("#type option[value=2]").hide().trigger("chosen:updated");
		$("#type option[value=3]").hide().trigger("chosen:updated");
		$("#type option[value=5]").hide().trigger("chosen:updated");
		$("#type option[value=6]").hide().trigger("chosen:updated");
		$("#type").val('4').trigger("chosen:updated");

		$(".fixed_percentage_type").html('');
		$(".fixed_percentage_type").hide();
		$(".Flexible_percentage_type").html($('.Flexible_percentage_type_div').html());
		$(".Flexible_percentage_type").show();
	}else{
		
		$("#type option[value=1]").show().trigger("chosen:updated");
		$("#type option[value=2]").show().trigger("chosen:updated");
		$("#type option[value=3]").show().trigger("chosen:updated");
		$("#type option[value=5]").show().trigger("chosen:updated");
		$("#type option[value=6]").show().trigger("chosen:updated");
		$(".Flexible_percentage_type").html('');
		$(".Flexible_percentage_type").hide();
		$(".fixed_percentage_type").html($('.fixed_percentage_type_div').html());
		$(".fixed_percentage_type").show();
	}
};