
var $ = jQuery.noConflict();
var RecordId = "";
var StatusId = "";

$(function () {
  "use strict";

  $.ajaxSetup({
    headers: {
      "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
    },
  });

  $(document).on(
    "click",
    ".tp_pagination nav ul.pagination a",
    function (event) {
      event.preventDefault();
      var page = $(this).attr("href").split("page=")[1];
      onPaginationDataLoad(page);
    }
  );
});
function onPaginationDataLoad(page) {
  $.ajax({
    url:
      base_url +
      "/backend/sellers/getSubscribedVendorsTableData?page=" +
      page +
      "&vendor_id=" +
      $("#vendor_id").val(),
    success: function (data) {
      $("#tp_datalist").html(data);
    },
  });
}

function onRefreshData() {
  var page = $(".pagination .active .page-link").html();
  if (page == "" || page == 0) {
    page = 1;
  }
  $.ajax({
    url:
      base_url +
      "/backend/sellers/getSubscribedVendorsTableData?search=" +
      "&vendor_id=" +
      $("#vendor_id").val(),
    success: function (data) {
      $("#tp_datalist").html(data);
    },
  });
}


function onChangeStatus(id, status) {
  RecordId = id;
  StatusId = status;
  var msg = TEXT["Do you really want to change this record"];
  onCustomModal(msg, "onConfirmUpdateStatus");
}

function onConfirmUpdateStatus() {
  $.ajax({
    type: "POST",
    url: base_url + "/backend/sellers/changeSubscribedVendorsStatus",
    data: "id=" + RecordId + "&status=" + StatusId,
    success: function (response) {
      var msgType = response.msgType;
      var msg = response.msg;

      if (msgType == "success") {
        onSuccessMsg(msg);
        onRefreshData();
      } else {
        onErrorMsg(msg);
      }
    },
  });
}