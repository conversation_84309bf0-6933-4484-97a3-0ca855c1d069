var $ = jQuery.noConflict();
var RecordId = '';
var BulkAction = '';
var ids = [];

$(function () {
	"use strict";

	$.ajaxSetup({
		headers: {
			'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
		}
	});
 
	resetForm("DataEntry_formId");
	
	$("#submit-form").on("click", function () {
        $("#DataEntry_formId").submit();
    });

	$(document).on('click', '.product_pagination .pagination a', function(event){
		event.preventDefault(); 
		var page = $(this).attr('href').split('page=')[1];
		onPaginationDataLoad(page);
	});
	
	$('input:checkbox').prop('checked',false);
	
    $(".checkAll").on("click", function () {
        $("input:checkbox").not(this).prop("checked", this.checked);
    });

	$("#title").on("blur", function () {
		if(RecordId ==''){
			onProductSlug();
		}
	});
	
	//$("#view_by_status").val(0);
	$("#store_id").val('all').trigger("chosen:updated");
	$("#voucher_type").val('all').trigger("chosen:updated");
	$("#main_status").val('all').trigger("chosen:updated");

	$("#store_id").on("change", function () {		
		onRefreshData();
	});
	$("#voucher_type").on("change", function () {		
		onRefreshData();
	});
	$("#main_status").on("change", function () {		
		onRefreshData();
	});
});

function onCheckAll() {
    $(".checkAll").on("click", function () {
        $("input:checkbox").not(this).prop("checked", this.checked);
    });
}

function onPaginationDataLoad(page) {
	$.ajax({
		url:base_url + "/backend/getProductsTableData/?page="+page
		+"&search="+$("#search").val()
		+"&store_id="+$('#store_id').val()
		+"&voucher_type="+$('#voucher_type').val()
		+"&sort_by="+$('#sort_by').val()
		+"&main_status="+$('#main_status').val(),
		success:function(data){
			$('#tp_datalist').html(data);
			onCheckAll();
		}
	});
}

function onRefreshData() {
	$.ajax({
		url: base_url + "/backend/getProductsTableData/?status="+$("#main_status").val()
		+"&search="+$("#search").val()
		+"&store_id="+$('#store_id').val()
		+"&voucher_type="+$('#voucher_type').val()
		+"&sort_by="+$('#sort_by').val()
		+"&main_status="+$('#main_status').val(),
		success:function(data){
			$('#tp_datalist').html(data);
			onCheckAll();
		}
	});
}

function onSearch() {
	$.ajax({
		url: base_url + "/backend/getProductsTableData/?search="+$("#search").val()
		+"&store_id="+$('#store_id').val()
		+"&voucher_type="+$('#voucher_type').val()
		+"&sort_by="+$('#sort_by').val()
		+"&main_status="+$('#main_status').val(),
		success:function(data){
			$('#tp_datalist').html(data);
			onCheckAll();
		}
	});
}

function onDataViewByStatus(status) {

	//$("#view_by_status").val(status);
	
	$(".viewstatus").removeClass('active')
	$("#viewstatus_"+status).addClass('active');
	
	$.ajax({
		url: base_url + "/backend/getProductsTableData/?status="+$("#main_status").val()
		+"&search="+$("#search").val()
		+"&store_id="+$('#store_id').val()
		+"&voucher_type="+$('#voucher_type').val()
		+"&sort_by="+$('#sort_by').val()
		+"&main_status="+$('#main_status').val(),
		success:function(data){
			$('#tp_datalist').html(data);
			onCheckAll();
		}
	});
}

function resetForm(id) {
    $('#' + id).each(function () {
        this.reset();
    });
	
	$("#lan").trigger("chosen:updated");
}

function onListPanel() {
	$('.parsley-error-list').hide();
    $('#list-panel, .btn-form').show();
    $('#form-panel, .btn-list').hide();
}

function onFormPanel() {
    resetForm("DataEntry_formId");
	RecordId = '';

	$("#lan").trigger("chosen:updated");
	
    $('#list-panel, .btn-form').hide();
    $('#form-panel, .btn-list').show();
}

function onEditPanel() {
    $('#list-panel, .btn-form').hide();
    $('#form-panel, .btn-list').show();	
}

function showPerslyError() {
    $('.parsley-error-list').show();
}

jQuery('#DataEntry_formId').parsley({
    listeners: {
        onFieldValidate: function (elem) {
            if (!$(elem).is(':visible')) {
                return true;
            }
            else {
                showPerslyError();
                return false;
            }
        },
        onFormSubmit: function (isFormValid, event) {
            if (isFormValid) {
                onConfirmWhenAddEdit();
                return false;
            }
        }
    }
});
function onConfirmWhenAddEdit() {

    $.ajax({
		type : 'POST',
		url: base_url + '/backend/saveProductsData',
		data: $('#DataEntry_formId').serialize(),
		success: function (response) {			
			var msgType = response.msgType;
			var msg = response.msg;

			if (msgType == "success") {
				resetForm("DataEntry_formId");
				onRefreshData();
				onSuccessMsg(msg);
				var id = response.id;
				window.location.href= base_url + '/backend/product/'+id;
			} else {
				onErrorMsg(msg);
			}
			
			onCheckAll();
		}
	});
}

function onDelete(id) {
	RecordId = id;
	var msg = TEXT["Do you really want to delete this record"];
	onCustomModal(msg, "onConfirmDelete");	
}

function onConfirmDelete() {

    $.ajax({
		type : 'POST',
		url: base_url + '/backend/deleteProducts',
		data: 'id='+RecordId,
		success: function (response) {
			var msgType = response.msgType;
			var msg = response.msg;

			if(msgType == "success"){
				onSuccessMsg(msg);
				onRefreshData();
			}else{
				onErrorMsg(msg);
			}
			
			onCheckAll();
		}
    });
}

function onBulkAction() {
	ids = [];
	$('.selected_item:checked').each(function(){
		ids.push($(this).val());
	});

	if(ids.length == 0){
		var msg = TEXT["Please select record"];
		onErrorMsg(msg);
		return;
	}
	
	BulkAction = $("#bulk-action").val();
	if(BulkAction == ''){
		var msg = TEXT["Please select action"];
		onErrorMsg(msg);
		return;
	}
	
	if(BulkAction == 'publish'){
		var msg = TEXT["Do you really want to publish this records"];
	}else if(BulkAction == 'draft'){
		var msg = TEXT["Do you really want to draft this records"];
	}else if(BulkAction == 'delete'){
		var msg = TEXT["Do you really want to delete this records"];
	}
	
	onCustomModal(msg, "onConfirmBulkAction");	
}

function onConfirmBulkAction() {

    $.ajax({
		type : 'POST',
		url: base_url + '/backend/bulkActionProducts',
		data: 'ids='+ids+'&BulkAction='+BulkAction,
		success: function (response) {
			var msgType = response.msgType;
			var msg = response.msg;

			if(msgType == "success"){
				onSuccessMsg(msg);
				onRefreshData();
				ids = [];
			}else{
				onErrorMsg(msg);
			}
			
			onCheckAll();
		}
    });
}

//Product Slug
function onProductSlug() {
	var StrName = $("#title").val();
	var str_name = StrName.trim();
	var strLength = str_name.length;
	if(strLength>0){
		$.ajax({
			type : 'POST',
			url: base_url + '/backend/hasProductSlug',
			data: 'slug='+StrName,
			success: function (response) {
				var slug = response.slug;
				$("#slug").val(slug);
			}
		});
	}
}

function checkSubCat(selectObject){
	var cat = selectObject.value;
	$(".sub_cat").hide();$("#subcategoryid").html('');
	$.ajax({
		type : 'GET',
		url: base_url + '/backend/getSubCategoryBYID/'+cat,
		data:'',
		success: function (data) {
			var i =0;
			var html = '<option value="">Choose Category</option>';
			$.each(data, function (key, obj) { i=1;
				html += '<option value="' + obj.id + '">' + obj.name + '</option>';
			});
			if(i==1){ 
				$(".sub_cat").show();
				$("#subcategoryid").html(html);
				$("#subcategoryid").chosen();
				$("#subcategoryid").trigger("chosen:updated");			
			}
		}
	});
}

function checkChildCat(selectObject){
	var cat = selectObject.value;
	$(".child_cat").hide();$("#childcategoryid").html('');
	$.ajax({
		type : 'GET',
		url: base_url + '/backend/getChildCategoryBYID/'+cat,
		data:'',
		success: function (data) {
			var i =0;
			var html = '<option value="">Choose Child Category</option>';
			$.each(data, function (key, obj) { i=1;
				html += '<option value="' + obj.id + '">' + obj.name + '</option>';
			});
			if(i==1){
				$(".child_cat").show();
				$("#childcategoryid").html(html);
				$("#childcategoryid").chosen();
				$("#childcategoryid").trigger("chosen:updated");			
			}
		}
	});
}

function checkSechSubCat(selectObject){
	var cat = selectObject.value;
	$(".sub_category_div").hide();$("#sub_category").html('');
	$(".child_category_div").hide();$("#child_category").html('');
	$.ajax({
		type : 'GET',
		url: base_url + '/backend/getSubCategoryBYID/'+cat,
		data:'',
		success: function (data) {
			var i =0;
			var html = '<option value="0">Choose Sub Category</option>';
			$.each(data, function (key, obj) { i=1;
				html += '<option value="' + obj.id + '">' + obj.name + '</option>';
			});
			if(i==1){
				$(".sub_category_div").show();
				$("#sub_category").html(html);
				$("#sub_category").chosen();
				$("#sub_category").trigger("chosen:updated");			
			}
		}
	});
}
function checkSechSubCatByID(id){
	var cat = id;
	$(".sub_category_div").hide();$("#sub_category").html('');
	$(".child_category_div").hide();$("#child_category").html('');
	$.ajax({
		type : 'GET',
		url: base_url + '/backend/getSubCategoryBYID/'+cat,
		data:'',
		success: function (data) {
			var i =0;
			var html = '<option value="0">Choose Sub Category</option>';
			$.each(data, function (key, obj) { i=1;
				html += '<option value="' + obj.id + '">' + obj.name + '</option>';
			});
			if(i==1){
				$(".sub_category_div").show();
				$("#sub_category").html(html);
				$("#sub_category").chosen();
				$("#sub_category").trigger("chosen:updated");			
			}
		}
	});
}

function checkSechChildCat(selectObject){
	var cat = selectObject.value;
	$(".child_category_div").hide();$("#child_category").html('');
	$.ajax({
		type : 'GET',
		url: base_url + '/backend/getChildCategoryBYID/'+cat,
		data:'',
		success: function (data) {
			var i =0;
			var html = '<option value="0">Choose Child Category</option>';
			$.each(data, function (key, obj) { i=1;
				html += '<option value="' + obj.id + '">' + obj.name + '</option>';
			});
			if(i==1){
				$(".child_category_div").show();
				$("#child_category").html(html);
				$("#child_category").chosen();
				$("#child_category").trigger("chosen:updated");			
			}
		}
	});
}
function chnagePrice(p_id){
	if(p_id!=''){
		$.ajax({
			type : 'GET',
			url: base_url + '/backend/getProductPrice/'+p_id,
			data:'',
			success: function (data) {
				$(".UpdateFishMarketPrice_p_sale_price").val(data);
				$(".UpdateFishMarketPrice_p_id").val(p_id);
				$(".product_price_change_popup_click").click();
			}
		});
	}
}


$("#media_select_file").on("click", function () {
		
	var thumbnail = $("#thumbnail").val();
	if(thumbnail !=''){
		$("#f_thumbnail_thumbnail").val(thumbnail);
		$("#view_thumbnail_image").html('<img src="'+public_path+'/media/'+thumbnail+'">');
	}

	$("#remove_f_thumbnail").show();
	$('#global_media_modal_view').modal('hide');
});

$("body").on('change','.storeID',function(){
	var storeID  = $(this).val();
	if(storeID!=''){
		$.ajax({
			url:base_url + "/backend/createSku/"+storeID,
			success:function(data){
				$(".skuData").val(data);							
			}
		});
	}
});