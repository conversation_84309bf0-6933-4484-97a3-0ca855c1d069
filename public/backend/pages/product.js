var $ = jQuery.noConflict();
var p_id;
$(function () {
	"use strict";

	$.ajaxSetup({
		headers: {
			'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
		}
	});

	$("#submit-form").on("click", function () {
        $("#DataEntry_formId").submit();
    });

	$("#product_name").on("blur", function () {
		onProductSlug();
	});

	$("#media_select_file").on("click", function () {
		
		var thumbnail = $("#thumbnail").val();
		if(thumbnail !=''){
			$("#f_thumbnail_thumbnail").val(thumbnail);
			$("#view_thumbnail_image").html('<img src="'+public_path+'/media/'+thumbnail+'">');
		}

		$("#remove_f_thumbnail").show();
		$('#global_media_modal_view').modal('hide');
    });
	
	$("#brand_id").chosen();
	$("#brand_id").trigger("chosen:updated");
	
	$("#cat_id").chosen();
	$("#cat_id").trigger("chosen:updated");
	
	$("#collection_id").chosen();
	$("#collection_id").trigger("chosen:updated");
	
	$("#label_id").chosen();
	$("#label_id").trigger("chosen:updated");
	
	$("#tax_id").chosen();
	$("#tax_id").trigger("chosen:updated");
	
	$("#is_featured").chosen();
	$("#is_featured").trigger("chosen:updated");
	
	//$("#lan").chosen();
	//$("#lan").trigger("chosen:updated");
	
	$("#is_publish").chosen();
	$("#is_publish").trigger("chosen:updated");
	
	$("#lan").on("change", function () {
		onCategoryList();
		onBrandList();
	});
	
	//Summernote
	// $('#description').summernote({
	// 	tabDisable: false,
	// 	height: 300,
	// 	toolbar: [
	// 	  ['style', ['style']],
	// 	  ['font', ['bold', 'italic', 'underline', 'clear']],
	// 		['color', ['color','forecolor']],
			
	// 	  ['para', ['ul', 'ol', 'paragraph']],
	// 	  ['table', ['table']],
	// 	  ['insert', ['link', 'unlink']],
	// 	  ['misc', ['undo', 'redo']],
	// 	  ['view', ['codeview', 'help']]
	// 	]
	// });	
	// $('#description_ar').summernote({
	// 	tabDisable: false,
	// 	height: 300,
	// 	toolbar: [
	// 	  ['style', ['style']],
	// 	  ['font', ['bold', 'italic', 'underline', 'clear']],
	// 	  ['color', ['color','forecolor']],
	// 	  ['para', ['ul', 'ol', 'paragraph']],
	// 	  ['table', ['table']],
	// 	  ['insert', ['link', 'unlink']],
	// 	  ['misc', ['undo', 'redo']],
	// 	  ['view', ['codeview', 'help']]
	// 	]
	// });	
});

function onMediaImageRemove(type) {
    $('#f_thumbnail_thumbnail').val('');
	$("#remove_f_thumbnail").hide();
}

function showPerslyError() {
    $('.parsley-error-list').show();
}

jQuery('#DataEntry_formId').parsley({
    listeners: {
        onFieldValidate: function (elem) {
            if (!$(elem).is(':visible')) {
                return true;
            } else {
                showPerslyError();
                return false;
            }
        },
        onFormSubmit: function (isFormValid, event) {
            if (isFormValid) {
                onConfirmWhenAddEdit();
                return false;
            }
        }
    }
});

function onConfirmWhenAddEdit() {

    var formData =  new FormData(document.getElementById("DataEntry_formId"));
	
    $.ajax({
		type : 'POST',
		url: base_url + '/backend/updateProductsData',
		data: formData,// $('#DataEntry_formId').serialize(),
		cache:false,
        contentType: false,
        processData: false,
		success: function (response) {			
			var msgType = response.msgType;
			var msg = response.msg;
			if (msgType == "success") {
				onSuccessMsg(msg);
				setTimeout(function() { 
					location.reload();	
				}, 500);
				
			} else {
				onErrorMsg(msg);
			}
		}
	});
}

//Product Slug
function onProductSlug() {
	var StrName = $("#product_name").val();
	var str_name = StrName.trim();
	var strLength = str_name.length;
	if(strLength>0){
		$.ajax({
			type : 'POST',
			url: base_url + '/backend/hasProductSlug',
			data: 'slug='+StrName,
			success: function (response) {
				var slug = response.slug;
				$("#slug").val(slug);
			}
		});
	}
}

function onCategoryList() {
	
	$.ajax({
		type : 'POST',
		url: base_url + '/backend/getCategoryList',
		data: 'lan='+$('#lan').val(),
		success: function (data) {
			var html = '<option value="" selected="selected">'+TEXT['Select Category']+'</option>';
			$.each(data, function (key, obj) {
				html += '<option value="' + obj.id + '">' + obj.name + '</option>';
			});
			
			$("#cat_id").html(html);
			$("#cat_id").chosen();
			$("#cat_id").trigger("chosen:updated");
		}
	});
}

function onBrandList() {
	
	$.ajax({
		type : 'POST',
		url: base_url + '/backend/getBrandList',
		data: 'lan='+$('#lan').val(),
		success: function (data) {
			var html = '<option value="0" selected="selected">No Brand</option>';
			$.each(data, function (key, obj) {
				html += '<option value="' + obj.id + '">' + obj.name + '</option>';
			});
			
			$("#brand_id").html(html);
			$("#brand_id").chosen();
			$("#brand_id").trigger("chosen:updated");
		}
	});
}
function checkChildCat(selectObject){
	var cat = selectObject.value;
	$(".child_cat").hide();$("#childcategoryid").html('');
	$.ajax({
		type : 'GET',
		url: base_url + '/backend/getChildCategoryBYID/'+cat,
		data:'',
		success: function (data) {
			var i =0;
			var html = '<option value="">Choose Child Category</option>';
			$.each(data, function (key, obj) { i=1;
				html += '<option value="' + obj.id + '">' + obj.name + '</option>';
			});
			if(i==1){
				$(".child_cat").show();
				$("#childcategoryid").html(html);
				$("#childcategoryid").chosen();
				$("#childcategoryid").trigger("chosen:updated");			
			}
		}
	});
}



$("body").on('change','.load_attachment',function(){
	var id = $(this).data('id');
	
	var files = $('#load_attachment_'+id)[0].files;
	 var count = files.length;
	 if(count!=0 && count !='0'){
		 $(".select_file_"+id).html('('+count+')');
		 $(".select_file_"+id).show();
	 }else{
		 $(".select_file_"+id).html('');
		 $(".select_file_"+id).hide();
	 }
 });
 function onDelete(id,in_id) {
	RecordId = id;
   // Record_in_id = in_id
	var msg = TEXT["Do you really want to delete this record"];
	onCustomModal(msg, "onConfirmDelete");	
}

function onConfirmDelete() {
    
    $.ajax({
		type : 'POST',
		url: base_url + '/backend/deleteProductImages',
		data: 'id='+RecordId,
		success: function (response) {
			var msgType = response.msgType;
			var msg = response.msg;
 
			if(msgType == "success"){
				onSuccessMsg(msg);
				//onRefreshData();
                $(".tp_thumb_"+RecordId).remove();
                
			}else{
				onErrorMsg(msg);
			}
		}
    });
}


function onVideoDelete(id,p_id) {
	alert
	RecordId = id;
    p_id = p_id;
	var msg = TEXT["Do you really want to delete this record"];
	onCustomModal(msg, "onConfirmVideoDelete");	
}

function onConfirmVideoDelete() {
    
    $.ajax({
		type : 'POST',
		url: base_url + '/backend/deleteProductVideo',
		data: 'id='+RecordId+'&p_id='+p_id,
		success: function (response) {
			var msgType = response.msgType;
			var msg = response.msg;

			if(msgType == "success"){
				onSuccessMsg(msg);
				//onRefreshData();
                $(".video_thumb_"+RecordId).remove();
                
			}else{
				onErrorMsg(msg);
			}
		}
    });
}
$("body").on('change','.ads_status_update',function(){
	var status 	=	$(this).val();
	if(status==2 || status=='2'){
		var data_html = $('.ads_rejection_reason_html').html();
		$(".ads_rejection_reason").html(data_html);
		$(".ads_rejection_reason").show();
	}else{

		$(".ads_rejection_reason").html('');
		$(".ads_rejection_reason").hide();
	}
})
$("body").on('change','.storeID',function(){
	var storeID  = $(this).val();
	if(storeID!=''){
		$.ajax({
			url:base_url + "/backend/createSku/"+storeID,
			success:function(data){
				$(".skuData").val(data);				
			}
		});
	}

});