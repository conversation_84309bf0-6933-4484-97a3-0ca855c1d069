var $ = jQuery.noConflict();
var Record_in_id = '';
$(function () {
	"use strict"; 

	$.ajaxSetup({
		headers: {
			'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
		}
	});

	$(".submit-form").on("click", function () {
        var id = $(this).data('id');
        //$("#DataEntry_formId_"+id).submit();
        onConfirmWhenAddEdit(id);
    });
    //Summernote
	$('.description').summernote({
		tabDisable: false,
		height: 300,
		toolbar: [
		  ['style', ['style']],
		  ['font', ['bold', 'italic', 'underline', 'clear']],
		  ['color', ['color']],
		  ['para', ['ul', 'ol', 'paragraph']],
		  ['table', ['table']],
		  ['insert', ['link', 'unlink']],
		  ['misc', ['undo', 'redo']],
		  ['view', ['codeview', 'help']]
		]
	});	
	
});
/*
function showPerslyError() {
    $('.parsley-error-list').show();
}

jQuery('.DataEntry_formId').parsley({
    listeners: {
        onFieldValidate: function (elem) {
            if (!$(elem).is(':visible')) {
                return true;
            }
            else {
                showPerslyError();
                return false;
            }
        },
        onFormSubmit: function (isFormValid, event) {
            if (isFormValid) {
                onConfirmWhenAddEdit();
                return false;
            }
        }
    }
});*/

function onConfirmWhenAddEdit(id) {

	// var input = document.getElementById("load_attachment_"+id);
    // var file = input.files;
    //file = JSON.stringify(file);
    //console.log(file);
    // var data = new FormData();
    //     data.append('FileName', document.getElementById("load_attachment_"+id).files);
    var formData =  new FormData(document.getElementById("DataEntry_formId_"+id));
    console.log(formData);
    $.ajax({
		type : 'POST',
		url: base_url + '/backend/saveInventoryData',
		data: formData,//$('#DataEntry_formId_'+id).serialize(),
        cache:false,
        contentType: false,
        processData: false,
		success: function (response) {			
			var msgType = response.msgType;
            var first = response.first;
			var msg = response.msg;
            if(response.id!=''){
                $.ajax({
                    type : 'GET',
                    url: base_url + '/backend/inventory_media/'+response.id,
                    data: '',//$('#DataEntry_formId_'+id).serialize(),
                    success: function (response) {	
                        $(".meida_galllery_div_un_"+id).html('');
                        $(".meida_galllery_div_un_"+id).html(response);
                    }
                });
            }
			if (first == "yes") {
				onSuccessMsg(msg);
                location.reload();
			}else if(msgType=='success'){
                onSuccessMsg(msg);
            }else{
				onErrorMsg(msg);
			}
		}
	});
}
function onDelete(id,in_id) {
	RecordId = id;
    Record_in_id = in_id
	var msg = TEXT["Do you really want to delete this record"];
	onCustomModal(msg, "onConfirmDelete");	
}

function onConfirmDelete() {
    
    $.ajax({
		type : 'POST',
		url: base_url + '/backend/deleteProductImages',
		data: 'id='+RecordId,
		success: function (response) {
			var msgType = response.msgType;
			var msg = response.msg;

			if(msgType == "success"){
				onSuccessMsg(msg);
				//onRefreshData();
                
                if(Record_in_id!=''){
                    $.ajax({
                        type : 'GET',
                        url: base_url + '/backend/inventory_media/'+Record_in_id,
                        data: '',//$('#DataEntry_formId_'+id).serialize(),
                        success: function (response) {	
                            $(".meida_galllery_div_un_"+Record_in_id).html('');
                            $(".meida_galllery_div_un_"+Record_in_id).html(response);
                        }
                    });
                }
			}else{
				onErrorMsg(msg);
			}
		}
    });
}

function onDeleteInventory(id,in_id) {
	deleteInventoryId = id;
    Record_in_id = in_id
	var msg = TEXT["Do you really want to delete this record"];
	onCustomModal(msg, "onConfirmDeleteInventory");	
}

function onConfirmDeleteInventory() {
    if(deleteInventoryId!=''){
        $.ajax({
            type : 'GET',
            url: base_url + '/backend/deleteInventoryPageData/'+deleteInventoryId,
            data: '',
            success: function (response) {
                var msgType = response.msgType;
                var msg = response.msg;

                if(msgType == "success"){
                    onSuccessMsg(msg);
                    $(".inventoryId_div_"+deleteInventoryId).hide();
                }else{
                    onErrorMsg(msg);
                }
            }
        });
    }
}

$("body").on('change','.load_attachment',function(){
   var id = $(this).data('id');
   
   var files = $('#load_attachment_'+id)[0].files;
    var count = files.length;
    if(count!=0 && count !='0'){
        $(".select_file_"+id).html('('+count+')');
        $(".select_file_"+id).show();
    }else{
        $(".select_file_"+id).html('');
        $(".select_file_"+id).hide();
    }
});