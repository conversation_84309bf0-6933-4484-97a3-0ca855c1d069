var $ = jQuery.noConflict();
var RecordId = '';
var BulkAction = '';
var ids = [];

$(function () {
	"use strict";

	$.ajaxSetup({
		headers: {
			'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
		}
	});

	resetForm("DataEntry_formId");
	
	$("#submit-form").on("click", function () {
        $("#DataEntry_formId").submit();
    });

	$(document).on('click', '.tp_pagination nav ul.pagination a', function(event){
		event.preventDefault(); 
		var page = $(this).attr('href').split('page=')[1];
		onPaginationDataLoad(page);
	});
	
	$('input:checkbox').prop('checked',false);
	
    $(".checkAll").on("click", function () {
        $("input:checkbox").not(this).prop("checked", this.checked);
    });

	$("#is_featured").chosen();
	$("#is_featured").trigger("chosen:updated");
	
	//$("#lan").chosen();
	//$("#lan").trigger("chosen:updated");
	
	$("#is_publish").chosen();
	$("#is_publish").trigger("chosen:updated");
	
	$("#media_select_file").on("click", function () {
		var LanguageIdUn = $("#global_media_modal_view .LanguageIdUn").val();
		
		if(LanguageIdUn=='eng' || LanguageIdUn==''){
			var thumbnail = $("#thumbnail").val();
			if(thumbnail !=''){
				$("#brand_thumbnail").val(thumbnail);
				$("#view_thumbnail_image").html('<img src="'+public_path+'/media/'+thumbnail+'">');
			}
		}else{
			var large_image = $("#large_image").val();
			if(large_image !=''){
				$("#offer_ads_image_ar").val(large_image);
				$("#view_offer_ads_image_ar").html('<img src="'+public_path+'/media/'+large_image+'">');
			}
			$("#remove_offer_ads_image_ar").show();
		}
		
		$("#remove_thumbnail").show();
		$('#global_media_modal_view').modal('hide');
    });
	
	//$("#language_code").val(0).trigger("chosen:updated");
	
	//$("#language_code").on("change", function () {
		//onRefreshData();
	//});
});

function onCheckAll() {
    $(".checkAll").on("click", function () {
        $("input:checkbox").not(this).prop("checked", this.checked);
    });
}

function onPaginationDataLoad(page) {
	$.ajax({
		url:base_url + "/backend/getpopupAdsTableData?page="+page
		+"&search="+$("#search").val(),
		//+"&language_code="+$('#language_code').val(),
		success:function(data){
			$('#tp_datalist').html(data);
			onCheckAll();
		}
	});
}

function onRefreshData() {
	var page = $(".pagination .active .page-link").html();
	if(page=='' || page==0){
		page=1;
	}
	$.ajax({
		url:base_url + "/backend/getpopupAdsTableData?search="+$("#search").val()+"&page="+page,
		//+"&language_code="+$('#language_code').val(),
		success:function(data){
			$('#tp_datalist').html(data);
			onCheckAll();
		}
	});
}

function onSearch() {
	$.ajax({
		url: base_url + "/backend/getpopupAdsTableData?search="+$("#search").val(),
		//+"&language_code="+$('#language_code').val(),,
		success:function(data){
			$('#tp_datalist').html(data);
			onCheckAll();
		}
	});
}

function resetForm(id) {
    $('#' + id).each(function () {
        this.reset();
    });
	
	$("#is_featured").trigger("chosen:updated");
	//$("#lan").trigger("chosen:updated");
	$("#is_publish").trigger("chosen:updated");
}

function onListPanel() {
	$('.parsley-error-list').hide();
    $('#list-panel, .btn-form').show();
    $('#form-panel, .btn-list').hide();
}

function onFormPanel() {
    resetForm("DataEntry_formId");
	RecordId = '';
	
	$("#remove_thumbnail").hide();
	$("#brand_thumbnail").html('');
	
	$("#is_featured").trigger("chosen:updated");
	//$("#lan").trigger("chosen:updated");
	$("#is_publish").trigger("chosen:updated");
	
    $('#list-panel, .btn-form').hide();
    $('#form-panel, .btn-list').show();
}

function onEditPanel() {
    $('#list-panel, .btn-form').hide();
    $('#form-panel, .btn-list').show();	
}

function onMediaImageRemove(type) {
    $('#brand_thumbnail').val('');
	$("#remove_thumbnail").hide();
}

function showPerslyError() {
    $('.parsley-error-list').show();
}

jQuery('#DataEntry_formId').parsley({
    listeners: {
        onFieldValidate: function (elem) {
            if (!$(elem).is(':visible')) {
                return true;
            }
            else {
                showPerslyError();
                return false;
            }
        },
        onFormSubmit: function (isFormValid, event) {
            if (isFormValid) {
                onConfirmWhenAddEdit();
                return false;
            }
        }
    }
});

function onConfirmWhenAddEdit() {

    $.ajax({
		type : 'POST',
		url: base_url + '/backend/savepopupAdsData',
		data: $('#DataEntry_formId').serialize(),
		success: function (response) {			
			var msgType = response.msgType;
			var msg = response.msg;

			if (msgType == "success") {
				resetForm("DataEntry_formId");
				onRefreshData();
				onSuccessMsg(msg);
				onListPanel();
			} else {
				onErrorMsg(msg);
			}
			
			onCheckAll();
		}
	});
}

function onEdit(id) {
	RecordId = id;
	var msg = TEXT["Do you really want to edit this record"];
	onCustomModal(msg, "onLoadEditData");	
}

function onLoadEditData() {

    $.ajax({
		type : 'POST',
		url: base_url + '/backend/getpopupAdsById',
		data: 'id='+RecordId,
		success: function (response) {
			var data = response;
			$("#RecordId").val(data.id);
			if(data.image != null){
				$("#brand_thumbnail").val(data.image);
				$("#view_thumbnail_image").html('<img src="'+public_path+'/media/'+data.image+'">');
				$("#remove_thumbnail").show();
			}else{
				$("#brand_thumbnail").val('');
				$("#view_thumbnail_image").html('');
				$("#remove_thumbnail").hide();
			}
			if(data.image_ar != null){
				$("#offer_ads_image_ar").val(data.image_ar);
				$("#view_offer_ads_image_ar").html('<img src="'+public_path+'/media/'+data.image_ar+'">');
				$("#remove_offer_ads_image_ar").show();
			}else{
				$("#offer_ads_image_ar").val('');
				$("#view_offer_ads_image_ar").html('');
				$("#remove_offer_ads_image_ar").hide();
			}
			$("#end_date").val(data.end_date);
			$("#is_publish").val(data.status).trigger("chosen:updated");
			$("#url").val(data.url);
			$("#cat_slug").val(data.cat_slug).trigger("chosen:updated");
			setTimeout(function(){
				getSubSlugsbyValue(data.cat_slug) ;
				setTimeout(function(){
					$("#sub_slug").val(data.sub_cat_slug).trigger("chosen:updated");
					getSubSlugsProductSlugVakue(data.sub_cat_slug)
					
				}, 1000);
			}, 1000);

			setTimeout(function(){
				$("#p_id").val(data.p_id).trigger("chosen:updated");
			}, 3000);
			
			
 			
			
			onEditPanel();
		}
    });
}

function onDelete(id) {
	RecordId = id;
	var msg = TEXT["Do you really want to delete this record"];
	onCustomModal(msg, "onConfirmDelete");	
}

function onConfirmDelete() {

    $.ajax({
		type : 'POST',
		url: base_url + '/backend/deletepopupAds',
		data: 'id='+RecordId,
		success: function (response) {
			var msgType = response.msgType;
			var msg = response.msg;

			if(msgType == "success"){
				onSuccessMsg(msg);
				onRefreshData();
			}else{
				onErrorMsg(msg);
			}
			
			onCheckAll();
		}
    });
}

function onBulkAction() {
	ids = [];
	$('.selected_item:checked').each(function(){
		ids.push($(this).val());
	});

	if(ids.length == 0){
		var msg = TEXT["Please select record"];
		onErrorMsg(msg);
		return;
	}
	
	BulkAction = $("#bulk-action").val();
	if(BulkAction == ''){
		var msg = TEXT["Please select action"];
		onErrorMsg(msg);
		return;
	}
	
	if(BulkAction == 'publish'){
		var msg = TEXT["Do you really want to publish this records"];
	}else if(BulkAction == 'draft'){
		var msg = TEXT["Do you really want to draft this records"];
	}else if(BulkAction == 'delete'){
		var msg = TEXT["Do you really want to delete this records"];
	}
	
	onCustomModal(msg, "onConfirmBulkAction");	
}

function onConfirmBulkAction() {

    $.ajax({
		type : 'POST',
		url: base_url + '/backend/bulkActionpopupAds',
		data: 'ids='+ids+'&BulkAction='+BulkAction,
		success: function (response) {
			var msgType = response.msgType;
			var msg = response.msg;

			if(msgType == "success"){
				onSuccessMsg(msg);
				onRefreshData();
				ids = [];
			}else{
				onErrorMsg(msg);
			}
			
			onCheckAll();
		}
    });
}

function getSubSlugs(selectObject){
	var slug = selectObject.value;

	if(slug!='' && slug!='null' && slug!=null ){
		$(".sub_slug_div").hide();
		$("#sub_slug").html('');
		$("#sub_slug").chosen();
		$("#sub_slug").trigger("chosen:updated");	
		$.ajax({
			type : 'GET',
			url: base_url + '/backend/getSubSlug/'+slug,
			data:'',
			success: function (data) {
				var i =0;
				var html = '<option value="">Select Sub Slug (Optional)</option>';
				$.each(data, function (key, obj) { i=1;
					html += '<option value="' + obj.slug + '">' + obj.name + '</option>';
				});
				if(i==1){
					$(".sub_slug_div").show();
					$("#sub_slug").html(html);
					$("#sub_slug").chosen();
					$("#sub_slug").trigger("chosen:updated");			
				}
			}
		});
		$("#p_id").html('');
		$("#p_id").chosen();
		$("#p_id").trigger("chosen:updated");	
		$.ajax({
			type : 'GET',
			url: base_url + '/backend/getProductbySlug/'+slug,
			data:'',
			success: function (data) {
				var i =0;
				var html = '<option value="">Select Product (Optional)</option>';
				$.each(data, function (key, obj) { i=1;
					html += '<option value="' + obj.id + '">' + obj.name + '</option>';
				});
				$("#p_id").html(html);
				$("#p_id").chosen();
				$("#p_id").trigger("chosen:updated");			
				
			}
		});	
	}
}
function getSubSlugsProduct(selectObject){
	var slug = selectObject.value;

	if(slug!='' && slug!='null' && slug!=null ){
		$("#p_id").html('');
		$("#p_id").chosen();
		$("#p_id").trigger("chosen:updated");	
		$.ajax({
			type : 'GET',
			url: base_url + '/backend/getProductbySubSlug/'+slug,
			data:'',
			success: function (data) {
				var i =0;
				var html = '<option value="">Select Product (Optional)</option>';
				$.each(data, function (key, obj) { i=1;
					html += '<option value="' + obj.id + '">' + obj.name + '</option>';
				});
				$("#p_id").html(html);
				$("#p_id").chosen();
				$("#p_id").trigger("chosen:updated");			
				
			}
		});	
	}
}
function getSubSlugsProductSlugVakue(slug){
	var slug = slug;

	if(slug!='' && slug!='null' && slug!=null ){
		$("#p_id").html('');
		$("#p_id").chosen();
		$("#p_id").trigger("chosen:updated");	
		$.ajax({
			type : 'GET',
			url: base_url + '/backend/getProductbySubSlug/'+slug,
			data:'',
			success: function (data) {
				var i =0;
				var html = '<option value="">Select Product (Optional)</option>';
				$.each(data, function (key, obj) { i=1;
					html += '<option value="' + obj.id + '">' + obj.name + '</option>';
				});
				$("#p_id").html(html);
				$("#p_id").chosen();
				$("#p_id").trigger("chosen:updated");			
				
			}
		});	
	}
}

function getSubSlugsbyValue(slug){
	
	var slug = slug;

	if(slug!='' && slug!='null' && slug!=null ){
		$(".sub_slug_div").hide();
		$("#sub_slug").html('');
		$("#sub_slug").chosen();
		$("#sub_slug").trigger("chosen:updated");	
		$.ajax({
			type : 'GET',
			url: base_url + '/backend/getSubSlug/'+slug,
			data:'',
			success: function (data) {
				var i =0;
				var html = '<option value="">Select Sub Slug (Optional)</option>';
				$.each(data, function (key, obj) { i=1;
					html += '<option value="' + obj.slug + '">' + obj.name + '</option>';
				});
				if(i==1){
					$(".sub_slug_div").show();
					$("#sub_slug").html(html);
					$("#sub_slug").chosen();
					$("#sub_slug").trigger("chosen:updated");			
				}
				
			}
		});
		$("#p_id").html('');
		$("#p_id").chosen();
		$("#p_id").trigger("chosen:updated");	
		$.ajax({
			type : 'GET',
			url: base_url + '/backend/getProductbySlug/'+slug,
			data:'',
			success: function (data) {
				var i =0;
				var html = '<option value="">Select Product (Optional)</option>';
				$.each(data, function (key, obj) { i=1;
					html += '<option value="' + obj.id + '">' + obj.name + '</option>';
				});
				$("#p_id").html(html);
				$("#p_id").chosen();
				$("#p_id").trigger("chosen:updated");			
				
			}
		});	
	}
}