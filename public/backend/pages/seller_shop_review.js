var $ = jQuery.noConflict();
var RecordId = "";
var BulkAction = "";
var ids = [];

$(function () {
    "use strict";

    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    $(document).on(
        "click",
        ".sellers_shop_review nav ul.pagination a",
        function (event) {
            event.preventDefault();
            var page = $(this).attr("href").split("page=")[1];
            onPaginationDataLoad(page);
        }
    );

});

function getData(user_id) {
    $.ajax({
      url: base_url + "/backend/getSellersShopReviewTableData/" + user_id,
      success: function (data) {
        $(".shop_review_information").html(data);
      },
    });
}


function onChangeStatus(shop_id, review_id, status) {
    var csrfToken = $('meta[name="csrf-token"]').attr("content");
    $.ajax({
      type: "POST",
      url: base_url + "/backend/changeSellerShopReviewStatus",
      data: JSON.stringify({
        review_id: review_id,
        status: status,
        _token: csrfToken,
      }), 
      contentType: "application/json",
      success: function (response) {
        var msgType = response.msgType;
        var msg = response.msg;
        if (msgType == "success") {
          onSuccessMsg(msg);
          getData(shop_id);
        } else {
          onErrorMsg(msg);
        }
      },
    });
}

function onPaginationDataLoad(page) {
    $.ajax({
      url:
        base_url +
        "/backend/getSellersShopReviewTableData/" +
        user_id +
        "?page=" +
        page,
      success: function (data) {
        $("#shop_review_information").html(data);
        onCheckAll();
      },
    });
}

function onCheckAll() {
    $(".checkAll").on("click", function () {
        $("input:checkbox").not(this).prop("checked", this.checked);
    });
}