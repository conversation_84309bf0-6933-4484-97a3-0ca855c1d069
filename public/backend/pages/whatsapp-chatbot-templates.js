
var $ = jQuery.noConflict();
var RecordId = "";
var ChatbotId = $("#chatbot_id").val();

$(function () {
    "use strict";

    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    resetForm("DataEntry_formId");

    $("#submit-form").on("click", function () {
        $("#DataEntry_formId").submit();
    });

    $(document).on("click", "ul.pagination a", function (event) {
        event.preventDefault();
        var page = $(this).attr("href").split("page=")[1];
        onPaginationDataLoad(page);
    });
});

function onPaginationDataLoad(page) {
    $.ajax({
        url:
            base_url +
            "/backend/whatsapp-chatbot/templates/get-table-data/" + ChatbotId + "?page=" +
            page +
            "&search=" +
            $("#search").val(),
        success: function (data) {
            $("#tp_datalist").html(data);
        },
    });
}

function onRefreshData() {
    var page = $(".pagination .active .page-link").html() || 1;
    $.ajax({
        url:
            base_url +
            "/backend/whatsapp-chatbot/templates/get-table-data/" + ChatbotId + "?search=" +
            $("#search").val() +
            "&page=" +
            page,
        success: function (data) {
            $("#tp_datalist").html(data);
        },
    });
}

function onSearch() {
    $.ajax({
        url:
            base_url +
            "/backend/whatsapp-chatbot/templates/get-table-data/" + ChatbotId + "?search=" +
            $("#search").val(),
        success: function (data) {
            $("#tp_datalist").html(data);
        },
    });
}

function resetForm(id) {
    $("#" + id).each(function () {
        this.reset();
    });
    $("#template_type").trigger("chosen:updated");
}

function onListPanel() {
    $(".parsley-error-list").hide();
    $("#list-panel, .btn-form").show();
    $("#form-panel, .btn-list").hide();
}

function onFormPanel() {
    resetForm("DataEntry_formId");
    RecordId = "";
    $("#list-panel, .btn-form").hide();
    $("#form-panel, .btn-list").show();
}

function onEditPanel() {
    $("#list-panel, .btn-form").hide();
    $("#form-panel, .btn-list").show();
}

function showParsleyError() {
    $(".parsley-error-list").show();
}

jQuery("#DataEntry_formId").parsley({
    listeners: {
        onFieldValidate: function (elem) {
            if (!$(elem).is(":visible")) {
                return true;
            } else {
                showParsleyError();
                return false;
            }
        },
        onFormSubmit: function (isFormValid, event) {
            if (isFormValid) {
                onConfirmWhenAddEdit();
                return false;
            }
        },
    },
});

function onConfirmWhenAddEdit() {
    $.ajax({
        type: "POST",
        url: base_url + "/backend/whatsapp-chatbot/templates/save-or-update",
        data: $("#DataEntry_formId").serialize(),
        success: function (response) {
            var msgType = response.msgType;
            var msg = response.msg;

            if (msgType === "success") {
                resetForm("DataEntry_formId");
                onRefreshData();
                onSuccessMsg(msg);
                onListPanel();
            } else {
                onErrorMsg(msg);
            }
        },
        error: function (xhr) {
            var errors = xhr.responseJSON.errors || {};
            var errorMsg = xhr.responseJSON.msg || "An error occurred";
            onErrorMsg(errorMsg + ": " + Object.values(errors).flat().join(", "));
        },
    });
}

function onEdit(id) {
    RecordId = id;
    var msg = TEXT["Do you really want to edit this record"];
    onCustomModal(msg, "onLoadEditData");
}

function onLoadEditData() {
    $.ajax({
        type: "POST",
        url: base_url + "/backend/whatsapp-chatbot/templates/get-template-by-id",
        data: "template_id=" + RecordId,
        dataType: "json",
        success: function (response) {
            var data = response.data;
            $("#template_id").val(data.id);
            $("#template_type").val(data.template_type).trigger("chosen:updated");
            $("#template_name").val(data.template_name);
            $("#header_text").val(data.header_text || "");
            $("#body_text").val(data.body_text);
            $("#footer_text").val(data.footer_text || "");
            $("#whatsapp_template_id").val(data.whatsapp_template_id || "");
            onEditPanel();
        },
    });
}

function onDelete(id) {
    RecordId = id;
    var msg = TEXT["Do you really want to delete this record"];
    onCustomModal(msg, "onConfirmDelete");
}

function onConfirmDelete() {
    $.ajax({
        type: "POST",
        url: base_url + "/backend/whatsapp-chatbot/" + ChatbotId + "/templates/delete/" + RecordId,
        data: "template_id=" + RecordId,
        success: function (response) {
            var msgType = response.msgType;
            var msg = response.msg;

            if (msgType === "success") {
                onSuccessMsg(msg);
                onRefreshData();
            } else {
                onErrorMsg(msg);
            }
        },
    });
}
