var $ = jQuery.noConflict();
var RecordId = "";
var BulkAction = "";
var ids = [];

$(function () {
  "use strict";

  $.ajaxSetup({
    headers: {
      "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
    },
  });

  resetForm("DataEntry_formId");

  $("#submit-form").on("click", function () {
    $("#DataEntry_formId").submit();
  });

  $(document).on("click", "ul.pagination a", function (event) {
    event.preventDefault();
    var page = $(this).attr("href").split("page=")[1];
    onPaginationDataLoad(page);
  });
});

function onPaginationDataLoad(page) {
  $.ajax({
      url:
          base_url +
          "/backend/whatsapp-chatbot/get-table-data?page=" +
          page +
          "&search=" +
          $("#search").val(),
      success: function (data) {
          $("#tp_datalist").html(data);
      },
  });
}

function onRefreshData() {
  var page = $(".pagination .active .page-link").html();
  if (page == "" || page == 0) {
    page = 1;
  }
  $.ajax({
      url:
          base_url +
          "/backend/whatsapp-chatbot/get-table-data?search=" +
          $("#search").val() +
          "&page=" +
          page,
      success: function (data) {
          $("#tp_datalist").html(data);
      },
  });
}

function onSearch() {
  $.ajax({
      url:
          base_url +
          "/backend/whatsapp-chatbot/get-table-data?search=" +
          $("#search").val(),
      success: function (data) {
          $("#tp_datalist").html(data);
      },
  });
}

function resetForm(id) {
  $("#" + id).each(function () {
    this.reset();
  });

  $("#lan").trigger("chosen:updated");
  $("#is_publish").trigger("chosen:updated");
  $("#is_subheader").trigger("chosen:updated");
}

function onListPanel() {
  $(".parsley-error-list").hide();
  $("#list-panel, .btn-form").show();
  $("#form-panel, .btn-list").hide();
}

function onFormPanel() {
  resetForm("DataEntry_formId");
  RecordId = "";

  $("#list-panel, .btn-form").hide();
  $("#form-panel, .btn-list").show();
}

function onEditPanel() {
  $("#list-panel, .btn-form").hide();
  $("#form-panel, .btn-list").show();
}

function showPerslyError() {
  $(".parsley-error-list").show();
}

jQuery("#DataEntry_formId").parsley({
  listeners: {
    onFieldValidate: function (elem) {
      if (!$(elem).is(":visible")) {
        return true;
      } else {
        showPerslyError();
        return false;
      }
    },
    onFormSubmit: function (isFormValid, event) {
      if (isFormValid) {
        onConfirmWhenAddEdit();
        return false;
      }
    },
  },
});

function onConfirmWhenAddEdit() {
  $.ajax({
      type: "POST",
      url: base_url + "/backend/whatsapp-chatbot/save-or-update",
      data: $("#DataEntry_formId").serialize(),
      success: function (response) {
          var msgType = response.msgType;
          var msg = response.msg;

          if (msgType == "success") {
              resetForm("DataEntry_formId");
              onRefreshData();
              onSuccessMsg(msg);
              onListPanel();
          } else {
              onErrorMsg(msg);
          }
          
      },
  });
}

function onEdit(id) {
  RecordId = id;
  var msg = TEXT["Do you really want to edit this record"];
  onCustomModal(msg, "onLoadEditData");
}

function onLoadEditData() {
  $.ajax({
      type: "POST",
      url: base_url + "/backend/whatsapp-chatbot/get-chatbot-by-id",
      data: "id=" + RecordId,
      success: function (response) {
          var data = response;
          $("#RecordId").val(data.id);
          $("#name").val(data.name);
          $("#description").val(data.description);

          onEditPanel();
      },
  });
}

function onDelete(id) {
  RecordId = id;
  var msg = TEXT["Do you really want to delete this record"];
  onCustomModal(msg, "onConfirmDelete");
}

function onConfirmDelete() {
  $.ajax({
    type: "POST",
    url: base_url + "/backend/deleteProductCategories",
    data: "id=" + RecordId,
    success: function (response) {
      var msgType = response.msgType;
      var msg = response.msg;

      if (msgType == "success") {
        onSuccessMsg(msg);
        onRefreshData();
      } else {
        onErrorMsg(msg);
      }
    },
  });
}
