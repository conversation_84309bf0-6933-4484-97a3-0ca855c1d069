var $ = jQuery.noConflict();
var RecordId = "";
var StatusId = "";

$(function () {
  "use strict";

  $.ajaxSetup({
    headers: {
      "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
    },
  });

  $(document).on(
    "click",
    ".tp_pagination nav ul.pagination a",
    function (event) {
      event.preventDefault();
      var page = $(this).attr("href").split("page=")[1];
      onPaginationDataLoad(page);
    }
  );

  $("input:checkbox").prop("checked", false);

  $(".checkAll").on("click", function () {
    $("input:checkbox").not(this).prop("checked", this.checked);
  });

  $("#is_featured").chosen();
  $("#is_featured").trigger("chosen:updated");

  $("#is_publish").chosen();
  $("#is_publish").trigger("chosen:updated");

  $("#media_select_file").on("click", function () {
    var thumbnail = $("#thumbnail").val();
    if (thumbnail != "") {
      $("#brand_thumbnail").val(thumbnail);
      $("#view_thumbnail_image").html(
        '<img src="' + public_path + "/media/" + thumbnail + '">'
      );
    }

    $("#remove_thumbnail").show();
    $("#global_media_modal_view").modal("hide");
  });
});

function onPaginationDataLoad(page) {
  $.ajax({
    url:
      base_url +
      "/backend/sellers/getSubscribedVendorsTableData?page=" +
      page +
      "&search=" +
      $("#search").val() +
      "&start_date=" +
      $("#start_date").val() +
      "&end_date=" +
      $("#end_date").val() +
      "&status=" +
      $("#status").val() +
      "&type=" +
      $("#type").val(),
    success: function (data) {
      $("#tp_datalist").html(data);
    },
  });
}

function onRefreshData() {
  var page = $(".pagination .active .page-link").html();
  if (page == "" || page == 0) {
    page = 1;
  }
  $.ajax({
    url:
      base_url +
      "/backend/sellers/getSubscribedVendorsTableData?search=" +
      $("#search").val() +
      "&page=" +
      page +
      "&start_date=" +
      $("#start_date").val() +
      "&end_date=" +
      $("#end_date").val() +
      "&status=" +
      $("#status").val() +
      "&type=" +
      $("#type").val(),
    success: function (data) {
      $("#tp_datalist").html(data);
    },
  });
}

function onSearch() {
  $.ajax({
    url:
      base_url +
      "/backend/sellers/getSubscribedVendorsTableData?search=" +
      $("#search").val() +
      "&start_date=" +
      $("#start_date").val() +
      "&end_date=" +
      $("#end_date").val() +
      "&status=" +
      $("#status").val() +
      "&type=" +
      $("#type").val(),
    success: function (data) {
      $("#tp_datalist").html(data);
    },
  });
}

function resetForm(id) {
  $("#" + id).each(function () {
    this.reset();
  });

  $("#is_featured").trigger("chosen:updated");
  $("#is_publish").trigger("chosen:updated");
}

function onListPanel() {
  $(".parsley-error-list").hide();
  $("#list-panel, .btn-form").show();
  $("#form-panel, .btn-list").hide();
}

function onChangeStatus(id, status) {
  RecordId = id;
  StatusId = status;
  var msg = TEXT["Do you really want to change this record"];
  onCustomModal(msg, "onConfirmUpdateStatus");
}

function onConfirmUpdateStatus() {
  $.ajax({
    type: "POST",
    url: base_url + "/backend/sellers/changeSubscribedVendorsStatus",
    data: "id=" + RecordId + "&status=" + StatusId,
    success: function (response) {
      var msgType = response.msgType;
      var msg = response.msg;

      if (msgType == "success") {
        onSuccessMsg(msg);
        onRefreshData();
      } else {
        onErrorMsg(msg);
      }
    },
  });
}