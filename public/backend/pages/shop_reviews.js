var $ = jQuery.noConflict();
var RecordId = '';
var BulkAction = '';
var ids = [];

$(function () {
	"use strict";

	$.ajaxSetup({
		headers: {
			'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
		}
	});
 

	$(document).on('click', '.product_pagination .pagination a', function(event){
		event.preventDefault(); 
		var page = $(this).attr('href').split('page=')[1];
		onPaginationDataLoad(page);
	});
	
	$("#store_id").val('all').trigger("chosen:updated");
	$("#customer_id").val('all').trigger("chosen:updated");
	$("#main_status").val('all').trigger("chosen:updated");

	$("#store_id").on("change", function () {		
		onRefreshData();
	});
	$("#customer_id").on("change", function () {		
		onRefreshData();
	});
	$("#main_status").on("change", function () {		
		onRefreshData();
	});
});


function onPaginationDataLoad(page) {
	$.ajax({
		url:base_url + "/backend/getShopReviewTableData/?page="+page
		+"&search="+$("#search").val()
		+"&store_id="+$('#store_id').val()
		+"&customer_id="+$('#customer_id').val()
		+"&sort_by="+$('#sort_by').val()
		+"&main_status="+$('#main_status').val(),
		success:function(data){
			$('#tp_datalist').html(data);
		}
	});
}

function onRefreshData() {
	
	$.ajax({
		url:base_url + "/backend/getShopReviewTableData/?main_status="+$("#main_status").val()
		+"&search="+$("#search").val()
		+"&store_id="+$('#store_id').val()
		+"&customer_id="+$('#customer_id').val()
		+"&sort_by="+$('#sort_by').val(),
		success:function(data){
			$('#tp_datalist').html(data);
		}
	});
}

function onSearch() {

	$.ajax({
		url: base_url + "/backend/getShopReviewTableData/?search="+$("#search").val()
		+"&store_id="+$('#store_id').val()		
		+"&customer_id="+$('#customer_id').val()
		+"&sort_by="+$('#sort_by').val()
		+"&main_status="+$('#main_status').val(),
		success:function(data){
			$('#tp_datalist').html(data);
		}
	});
}

function onChangeStatus(review_id, status) {
  var csrfToken = $('meta[name="csrf-token"]').attr("content");
  $.ajax({
    type: "POST",
    url: base_url + "/backend/changeShopReviewStatus",
    data: JSON.stringify({
      review_id: review_id,
      status: status,
      _token: csrfToken,
    }),
    contentType: "application/json",
    success: function (response) {
      var msgType = response.msgType;
      var msg = response.msg;
      if (msgType == "success") {
        onSuccessMsg(msg);
        onRefreshData();
      } else {
        onErrorMsg(msg);
      }
    },
  });
}
