var $ = jQuery.noConflict();
var RecordId = "";
var BulkAction = "";
var ids = [];

$(function () {
    "use strict";

    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    $(document).on(
        "click",
        ".sellers_banner nav ul.pagination a",
        function (event) {
            event.preventDefault();
            var page = $(this).attr("href").split("page=")[1];
            onPaginationBannerDataLoad(page);
        }
    );

});

function getBannerData(user_id) {
    $.ajax({
        url: base_url + "/backend/getSellersBannerTableData/" + user_id,
        success: function (data) {
            $(".banner_information").html(data);
        },
    });
}
$(".add_new_banner_form").submit(function (e) {
    e.preventDefault();

    return false;
});

$("body").on("click", ".addBannerBut", function () {
    var form = $('.add_new_banner_form')[0];
    var formData = new FormData(form);   
   $.ajax({
     type: "POST",
     url: base_url + "/backend/saveSellersBannerData",
     data: formData,
     cache: false,
     contentType: false,
     processData: false,
     success: function (response) {
       var msgType = response.msgType;
       var msg = response.msg;
       var id = response.id;
       if (msgType == "success") {
         $(".addNewBannerModel").modal("toggle");
         $(".modal-backdrop").removeClass("show");
           onSuccessMsg(msg, "Ok, got it!");
           getBannerData(id);
       } else {
         onErrorMsg(msg, "Ok, got it!");
       }
     },
   });
});
function onBannerEdit(banner_id) {
    $.ajax({
        type: "get",
        url: base_url + "/backend/getSellerBannerById/" + banner_id,
        data: {},
        success: function (response) {
            var msgType = response.msgType;
            var msg = response.msg;
            var data = response.data;
            if (msgType == "success") {
                $(".edit_new_banner_form #banner_id").val(data.id);
                $(".edit_new_banner_form #status").val(data.status);
                $("#banner_image_en_show").attr("href", base_url + '/media/shop_banner/' + data.banner_image_en).find("img").attr("src", base_url + '/media/shop_banner/' + data.banner_image_en);
                $("#banner_image_ar_show").attr("href", base_url + '/media/shop_banner/' + data.banner_image_ar).find("img").attr("src", base_url + '/media/shop_banner/' + data.banner_image_ar);
                $(".editNewBannerModel").modal("toggle");
            } else {
                onErrorMsg(msg);
            }
        },
    });
    
}
$("body").on("click", ".updateBannerBut", function () {
    var form = $('.edit_new_banner_form')[0];
    var formData = new FormData(form);   
   $.ajax({
     type: "POST",
     url: base_url + "/backend/saveSellerBannerById",
     data: formData,
     cache: false,
     contentType: false,
     processData: false,
     success: function (response) {
       var msgType = response.msgType;
       var msg = response.msg;
       var id = response.id;
       if (msgType == "success") {
         $(".editNewBannerModel").modal("toggle");
         $(".modal-backdrop").removeClass("show");
           onSuccessMsg(msg, "Ok, got it!");
           getBannerData(id);
       } else {
         onErrorMsg(msg, "Ok, got it!");
       }
     },
   });
    
});
function onBannerDelete(id, shop_id) {
    RecordId = id;
    shopID = shop_id;
    var msg = "Do you really want to delete this banner";
    onCustomModal(msg, "onConfirmBannerDelete");
}

function onConfirmBannerDelete() {
    $.ajax({
        type: "POST",
        url: base_url + "/backend/deleteSellerBanner",
        data: "id=" + RecordId + "&shop_id=" + shopID,
        success: function (response) {
            var msgType = response.msgType;
            var msg = response.msg;
            var shop_id = response.id;
            if (msgType == "success") {
                onSuccessMsg(msg);
                getBannerData(shop_id);
            } else {
                onErrorMsg(msg);
            }
        },
    });
}

function onPaginationBannerDataLoad(page) {
    $.ajax({
        url:
            base_url +
            "/backend/getSellersBannerTableData/" +
            user_id +
            "?page=" +
            page +
            "&search=" +
            $("#search").val() +
            "&status=" +
            $("#status").val() +
            "&sort_by=" +
            $("#sort_by").val(),
        success: function (data) {
            $("#banner_information").html(data);
            onCheckAll();
        },
    });
}

function onCheckAll() {
    $(".checkAll").on("click", function () {
        $("input:checkbox").not(this).prop("checked", this.checked);
    });
}