var $ = jQuery.noConflict();

$(function () {
    "use strict";

    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    onLoadEditData();

    // Toggle password visibility
    $(".toggle-password").on("click", function () {
        $(this).toggleClass("fa-eye-slash");
        let input = $($(this).attr("toggle"));
        if (input.attr("type") == "password") {
            input.attr("type", "text");
        } else {
            input.attr("type", "password");
        }
    });

    $("#remove_photo_thumbnail").hide();

    $("#on_thumbnail").on("click", function () {
        onGlobalMediaModalView();
    });

    $("#media_select_file").on("click", function () {
        var thumbnail = $("#thumbnail").val();
        if (thumbnail != "") {
            $("#photo_thumbnail").val(thumbnail);
            $("#view_photo_thumbnail").html(
                '<img src="' + public_path + "/media/" + thumbnail + '">'
            );
        }
        $("#remove_photo_thumbnail").show();
        $("#global_media_modal_view").modal("hide");
    });

    // Initialize Parsley for profileForm
    if (!$("#profileForm").hasClass("parsley-validated")) {
        $("#profileForm").parsley({
            listeners: {
                onFieldValidate: function (elem) {
                    if (!$(elem).is(":visible")) {
                        return true;
                    } else {
                        showPerslyError();
                        return false;
                    }
                },
                onFormSubmit: function (isFormValid, event) {
                    event.preventDefault(); // Prevent native form submission
                    if (isFormValid) {
                        onConfirmWhenAddEdit();
                    }
                },
            },
        });
    }

    // Handle change-password-form submission
    $("#change-password-form").on("submit", function (e) {
        e.preventDefault();
        onConfirmWhenChangePassword();
    });
});

function onMediaImageRemove(type) {
    $("#photo_thumbnail").val("");
    $("#remove_photo_thumbnail").hide();
}

function showPerslyError() {
    $(".parsley-error-list").show();
}

function onConfirmWhenAddEdit() {
    var $submitButton = $("#submitProfile");
    $submitButton.prop("disabled", true); // Disable button to prevent multiple clicks
    $.ajax({
        type: "POST",
        url: base_url + "/backend/profile-update",
        data: $("#profileForm").serialize(),
        success: function (response) {
            var msgType = response.msgType;
            var msg = response.msg;
            if (msgType == "success") {
                onSuccessMsg(msg);
                onLoadEditData();
            } else {
                onErrorMsg(msg);
            }
        },
        complete: function () {
            $submitButton.prop("disabled", false); // Re-enable button
        },
    });
}

function onLoadEditData() {
    $.ajax({
        type: "POST",
        url: base_url + "/backend/get-user-by-id",
        data: "id=" + userid,
        success: function (response) {
            var data = response;
            $("#RecordId").val(data.id);
            $("#name").val(data.name);
            $("#email").val(data.email);
            $("#phone").val(data.phone);
            if (data.photo != null) {
                $("#photo_thumbnail").val(data.photo);
                $("#view_photo_thumbnail").html(
                    '<img src="' + public_path + "/media/" + data.photo + '">'
                );
                $("#remove_photo_thumbnail").show();
            } else {
                $("#photo_thumbnail").val("");
                $("#view_photo_thumbnail").html("");
                $("#remove_photo_thumbnail").hide();
            }
        },
    });
}

function onConfirmWhenChangePassword() {
    var $submitButton = $("#submitPassword");
    $submitButton.prop("disabled", true);
    $.ajax({
        type: "POST",
        url: base_url + "/backend/change-password",
        data: $("#change-password-form").serialize(),
        success: function (response) {
            var msgType = response.msgType;
            var msg = response.msg;
            if (msgType == "success") {
                $("#change-password-form")[0].reset();
                onSuccessMsg(msg);
            } else {
                onErrorMsg(msg);
            }
        },
        complete: function () {
            $submitButton.prop("disabled", false);
        },
    });
}
