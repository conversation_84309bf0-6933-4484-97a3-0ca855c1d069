var $ = jQuery.noConflict();

$(function () {  
	"use strict";

	$.ajaxSetup({
		headers: {
			'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
		}
	});
	
	$(document).on('click', '.users_pagination nav ul.pagination a', function(event){
		event.preventDefault(); 
		var page = $(this).attr('href').split('page=')[1];
		onPaginationDataLoad(page);
	});
	
	$("#status").chosen();
	$("#status").trigger("chosen:updated");
	
	$("#sort_by").chosen();
	$("#sort_by").trigger("chosen:updated");
});

function onPaginationDataLoad(page) {
	var status =	$("#status").val();
	var sort_by =	$("#sort_by").val();
	$.ajax({
		url:base_url + "/backend/getMessageLogTableData?page="+page+"&status="+status+'&sort_by='+sort_by,
		success:function(data){
			$('#tp_datalist').html(data);
		}
	});
}

function onRefreshData() {
	var status =	$("#status").val();
	var sort_by =	$("#sort_by").val();
	$.ajax({
		url:base_url + "/backend/getMessageLogTableData?status="+status+'&sort_by='+sort_by,
		success:function(data){
			$('#tp_datalist').html(data);
		}
	});
}

function onSearch() {
	var status =	$("#status").val();
	var sort_by =	$("#sort_by").val();
	$.ajax({
		url: base_url + "/backend/getMessageLogTableData?status="+status+'&sort_by='+sort_by,
		success:function(data){
			$('#tp_datalist').html(data);
		}
	});
}



