function onPaginationDataLoad(page = 0) {
    $.ajax({
        url:
            base_url +
            "/backend/getPaymentHistoryTableData?page=" +
            page +
            "&search=" +
            $("#search").val() +
            "&store_id=" +
            $("#store_id").val() +
            "&user_id=" +
            $("#user_id").val() +
            "&start_date=" +
            $("#start_date").val() +
            "&end_date=" +
            $("#end_date").val() +
            "&sort_by=" +
            $("#sort_by").val() +
            "&type=" +
            $("#type").val(),
        //+"&language_code="+$('#language_code').val(),
        success: function (data) {
            $("#tp_datalist").html(data);
        },
    });
}

$(document).on("click", ".tp_pagination nav ul.pagination a", function (event) {
    event.preventDefault();
    var page = $(this).attr("href").split("page=")[1];
    onPaginationDataLoad(page);
});

function onSearch() {
    onPaginationDataLoad();
}
onPaginationDataLoad();
